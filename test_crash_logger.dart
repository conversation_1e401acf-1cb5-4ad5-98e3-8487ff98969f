import 'package:flutter/foundation.dart';
import 'lib/utils/crash_logger.dart';

void main() async {
  print('Testing CrashLogger...');
  
  // 初始化
  CrashLogger.init();
  
  // 等待一下让初始化完成
  await Future.delayed(Duration(seconds: 1));
  
  // 添加测试日志
  CrashLogger.addTestLog();
  
  // 等待保存完成
  await Future.delayed(Duration(seconds: 1));
  
  // 检查日志是否存在
  final exists = await CrashLogger.logFileExists();
  print('Log file exists: $exists');
  
  // 获取日志路径
  final path = await CrashLogger.getLogFilePath();
  print('Log path: $path');
  
  // 获取所有日志
  final logs = await CrashLogger.instance.getAllLogs();
  print('Total logs: ${logs.length}');
  
  for (int i = 0; i < logs.length; i++) {
    print('Log ${i + 1}: ${logs[i].type} - ${logs[i].shortError}');
  }
  
  // 获取日志文件大小
  final size = await CrashLogger.instance.getLogFileSize();
  print('Log file size: $size');
  
  print('Test completed!');
}
