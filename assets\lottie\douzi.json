{"skeleton": {"hash": "FhLqpMULJBo", "spine": "4.2.39", "x": -341.15, "y": -336.08, "width": 679.17, "height": 676.08, "images": "", "audio": "./audio"}, "bones": [{"name": "root", "y": -162.99}, {"name": "bone", "parent": "root", "y": 162.99}, {"name": "图层 3assdd", "parent": "bone"}, {"name": "gx", "parent": "bone", "x": 552.84, "y": -471.08}, {"name": "gx2", "parent": "bone", "x": 552.84, "y": -471.08}, {"name": "gx3", "parent": "bone", "x": 552.84, "y": -471.08}], "slots": [{"name": "root", "bone": "bone", "attachment": "root"}, {"name": "图层 3assdd", "bone": "图层 3assdd", "attachment": "图层 3assdd"}, {"name": "gx", "bone": "gx", "attachment": "gx"}, {"name": "gx2", "bone": "gx2", "color": "ffffff6b", "attachment": "gx"}, {"name": "gx3", "bone": "gx3", "color": "ffffff40", "attachment": "gx"}], "skins": [{"name": "default", "attachments": {"gx": {"gx": {"width": 568, "height": 568}}, "gx2": {"gx": {"width": 568, "height": 568}}, "gx3": {"gx": {"width": 568, "height": 568}}, "root": {"root": {"type": "clipping", "end": "root", "vertexCount": 47, "vertices": [41.81, 340, 95.88, 329.37, 140.36, 312.05, 166.18, 299.06, 190.31, 284.77, 220.97, 261.81, 248.24, 235.69, 273.95, 205.47, 294.44, 173.42, 314.49, 132.97, 327.34, 93.06, 336.44, 48.79, 338.02, -3.1, 334.42, -40.6, 328.43, -74.56, 312.42, -123.24, 291.4, -169.17, 267, -203.17, 252.08, -221.95, 212.3, -261.77, 175.8, -286.27, 141.69, -306, 83.56, -327.6, 31.92, -336.06, -32.44, -336.08, -74.81, -330.08, -101.33, -322.7, -140.8, -308.19, -185.97, -283.67, -233.16, -248.12, -269.19, -207.55, -298.97, -163.45, -321.22, -113.83, -333.36, -72.9, -341.08, -26.82, -341.15, 26.43, -335.27, 72.83, -322.29, 114.3, -308.4, 147.1, -292.09, 177.25, -266.68, 214.75, -230.54, 253.68, -197.99, 278.91, -158.43, 304.57, -96.17, 328.22, -60.46, 336.2, -28.31, 340], "color": "ce3a3aff"}}, "图层 3assdd": {"图层 3assdd": {"type": "mesh", "uvs": [0.56077, 0, 0.63936, 0.01564, 0.70401, 0.0411, 0.74154, 0.06021, 0.77662, 0.08122, 0.82118, 0.11498, 0.86082, 0.1534, 0.89818, 0.19784, 0.92797, 0.24497, 0.95711, 0.30445, 0.97579, 0.36314, 0.98902, 0.42824, 0.99131, 0.50455, 0.98607, 0.5597, 0.97736, 0.60965, 0.9541, 0.68123, 0.92354, 0.74878, 0.88809, 0.79877, 0.86639, 0.8264, 0.80857, 0.88495, 0.75553, 0.92099, 0.70595, 0.94999, 0.62145, 0.98176, 0.5464, 0.99421, 0.45284, 0.99423, 0.39126, 0.98541, 0.35272, 0.97455, 0.29536, 0.95323, 0.2297, 0.91716, 0.16111, 0.86488, 0.10873, 0.80523, 0.06546, 0.74036, 0.03312, 0.6674, 0.01546, 0.60721, 0.00425, 0.53945, 0.00415, 0.46113, 0.01269, 0.3929, 0.03155, 0.33192, 0.05174, 0.28368, 0.07544, 0.23933, 0.11238, 0.18419, 0.16492, 0.12695, 0.21222, 0.08984, 0.26972, 0.05211, 0.36022, 0.01732, 0.41213, 0.00558, 0.45884, 0], "triangles": [37, 34, 35, 12, 13, 10, 12, 10, 11, 36, 37, 35, 33, 34, 37, 10, 13, 8, 38, 33, 37, 32, 33, 38, 7, 8, 13, 10, 8, 9, 14, 15, 13, 38, 30, 32, 30, 31, 32, 6, 7, 15, 13, 15, 7, 6, 15, 42, 16, 17, 15, 41, 30, 39, 39, 30, 38, 2, 46, 0, 42, 44, 45, 43, 44, 42, 1, 2, 0, 15, 17, 42, 40, 41, 39, 42, 29, 30, 46, 42, 45, 3, 46, 2, 42, 46, 6, 46, 3, 6, 5, 3, 4, 41, 42, 30, 3, 5, 6, 18, 28, 42, 28, 29, 42, 17, 18, 42, 18, 20, 28, 19, 20, 18, 28, 26, 27, 28, 20, 26, 24, 25, 26, 20, 21, 23, 24, 26, 20, 21, 22, 23, 20, 23, 24], "vertices": [41.81, 340, 95.88, 329.37, 140.36, 312.05, 166.18, 299.06, 190.31, 284.77, 220.97, 261.81, 248.24, 235.69, 273.95, 205.47, 294.44, 173.42, 314.49, 132.97, 327.34, 93.06, 336.44, 48.79, 338.02, -3.1, 334.42, -40.6, 328.43, -74.56, 312.42, -123.24, 291.4, -169.17, 267, -203.17, 252.08, -221.95, 212.3, -261.77, 175.8, -286.27, 141.69, -306, 83.56, -327.6, 31.92, -336.06, -32.44, -336.08, -74.81, -330.08, -101.33, -322.7, -140.8, -308.19, -185.97, -283.67, -233.16, -248.12, -269.19, -207.55, -298.97, -163.45, -321.22, -113.83, -333.36, -72.9, -341.08, -26.82, -341.15, 26.43, -335.27, 72.83, -322.29, 114.3, -308.4, 147.1, -292.09, 177.25, -266.68, 214.75, -230.54, 253.68, -197.99, 278.91, -158.43, 304.57, -96.17, 328.22, -60.46, 336.2, -28.31, 340], "hull": 47, "edges": [0, 92, 0, 2, 2, 4, 4, 6, 6, 8, 8, 10, 10, 12, 12, 14, 14, 16, 16, 18, 18, 20, 20, 22, 22, 24, 24, 26, 26, 28, 28, 30, 30, 32, 32, 34, 34, 36, 36, 38, 38, 40, 40, 42, 42, 44, 44, 46, 46, 48, 48, 50, 50, 52, 52, 54, 54, 56, 56, 58, 58, 60, 60, 62, 62, 64, 64, 66, 66, 68, 68, 70, 70, 72, 72, 74, 74, 76, 76, 78, 78, 80, 80, 82, 82, 84, 84, 86, 86, 88, 88, 90, 90, 92], "width": 688, "height": 680}}}}], "animations": {"animation": {"slots": {"gx": {"rgba": [{"time": 2.8667, "color": "ffffffff"}, {"time": 3, "color": "ffffff00", "curve": "stepped"}, {"time": 6.6667, "color": "ffffff00"}, {"time": 6.7333, "color": "ffffffff", "curve": "stepped"}, {"time": 6.9333, "color": "ffffffff"}, {"time": 7.0667, "color": "ffffff00"}]}, "gx2": {"rgba": [{"time": 3.1, "color": "ffffff6b"}, {"time": 3.3667, "color": "ffffff00", "curve": "stepped"}, {"time": 6.9, "color": "ffffff00"}, {"time": 6.9667, "color": "ffffffff"}, {"time": 7.1667, "color": "ffffff6b"}, {"time": 7.4333, "color": "ffffff00"}]}, "gx3": {"rgba": [{"time": 3.8, "color": "ffffffff"}, {"time": 4, "color": "ffffff6b"}, {"time": 4.2333, "color": "ffffff00", "curve": "stepped"}, {"time": 7.8, "color": "ffffff00"}, {"time": 7.8667, "color": "ffffffff"}, {"time": 8.0667, "color": "ffffff6b"}, {"time": 8.2333, "color": "ffffff00"}]}}, "bones": {"gx": {"scale": [{"time": 2.6667}, {"time": 2.8667, "x": 4, "y": 4}, {"time": 6.7333}, {"time": 6.9333, "x": 4, "y": 4}]}, "bone": {"rotate": [{"time": 2.3333, "curve": [2.444, 0, 2.556, -30]}, {"time": 2.6667, "value": -30, "curve": [2.778, -30, 2.889, 30]}, {"time": 3, "value": 30, "curve": [3.111, 30, 3.222, -20]}, {"time": 3.3333, "value": -20, "curve": [3.389, -20, 3.444, 0]}, {"time": 3.5, "curve": "stepped"}, {"time": 6.4667, "curve": [6.583, 0, 6.717, -30]}, {"time": 6.8333, "value": -30, "curve": [6.944, -30, 7.056, 30]}, {"time": 7.1667, "value": 30, "curve": [7.281, 30, 7.419, -20]}, {"time": 7.5333, "value": -20, "curve": [7.589, -20, 7.644, 0]}, {"time": 7.7}], "translate": [{"curve": [0.222, 0, 0.444, 0, 0.222, -66.67, 0.444, -150]}, {"time": 0.6667, "y": -150, "curve": [0.889, 0, 1.111, 0, 0.889, -150, 1.111, 0]}, {"time": 1.3333, "curve": [1.389, 0, 1.944, 0, 1.389, 0, 1.944, -150]}, {"time": 2, "y": -150, "curve": [2.222, 0, 2.444, 0, 2.222, -150, 2.444, -66.67]}, {"time": 2.6667, "curve": [2.889, 0, 3.278, 0, 2.889, 66.67, 3.278, -150]}, {"time": 3.3333, "y": -150, "curve": [3.556, 0, 3.778, 0, 3.556, -150, 3.778, -66.67]}, {"time": 4, "curve": [4.222, 0, 4.611, 0, 4.222, 66.67, 4.611, -150]}, {"time": 4.6667, "y": -150, "curve": [4.899, 0, 5.167, 0, 4.899, -150, 5.167, -69.84]}, {"time": 5.4, "curve": [5.633, 0, 6.075, 0, 5.633, 69.84, 6.075, -150]}, {"time": 6.1333, "y": -150, "curve": [6.361, 0, 6.606, 0, 6.361, -150, 6.606, -68.29]}, {"time": 6.8333, "curve": [7.061, 0, 7.306, 0, 7.061, 68.29, 7.306, -150]}, {"time": 7.5333, "y": -150, "curve": [7.756, 0, 7.978, 0, 7.756, -150, 7.978, -66.67]}, {"time": 8.2}]}, "gx2": {"scale": [{"time": 2.9}, {"time": 3.1, "x": 4, "y": 4}, {"time": 6.9667}, {"time": 7.1667, "x": 4, "y": 4}]}, "gx3": {"scale": [{"time": 3.8}, {"time": 4, "x": 4, "y": 4}, {"time": 7.8667}, {"time": 8.0667, "x": 4, "y": 4}]}}, "drawOrder": [{"time": 4.9333, "offsets": [{"slot": "root", "offset": 1}]}]}}}