PODS:
  - Adjust (5.4.1):
    - Adjust/Adjust (= 5.4.1)
  - Adjust/Adjust (5.4.1):
    - AdjustSignature (= 3.47.0)
  - adjust_sdk (5.4.1):
    - Adjust (= 5.4.1)
    - Flutter
  - AdjustSignature (3.47.0)
  - AppAuth (1.7.6):
    - AppAuth/Core (= 1.7.6)
    - AppAuth/ExternalUserAgent (= 1.7.6)
  - AppAuth/Core (1.7.6)
  - AppAuth/ExternalUserAgent (1.7.6):
    - AppAuth/Core
  - audio_session (0.0.1):
    - Flutter
  - barcode_scan2 (0.0.1):
    - Flutter
    - MTBBarcodeScanner
    - SwiftProtobuf
  - camera_avfoundation (0.0.1):
    - Flutter
  - connectivity_plus (0.0.1):
    - Flutter
    - ReachabilitySwift
  - device_info_plus (0.0.1):
    - Flutter
  - FBAEMKit (17.0.3):
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit (17.0.3):
    - FBAEMKit (= 17.0.3)
    - FBSDKCoreKit_Basics (= 17.0.3)
  - FBSDKCoreKit_Basics (17.0.3)
  - FBSDKLoginKit (17.0.3):
    - FBSDKCoreKit (= 17.0.3)
  - Firebase/Analytics (10.22.0):
    - Firebase/Core
  - Firebase/Core (10.22.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.22.0)
  - Firebase/CoreOnly (10.22.0):
    - FirebaseCore (= 10.22.0)
  - Firebase/Messaging (10.22.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.22.0)
  - firebase_analytics (10.8.9):
    - Firebase/Analytics (= 10.22.0)
    - firebase_core
    - Flutter
  - firebase_core (2.27.0):
    - Firebase/CoreOnly (= 10.22.0)
    - Flutter
  - firebase_messaging (14.7.19):
    - Firebase/Messaging (= 10.22.0)
    - firebase_core
    - Flutter
  - FirebaseAnalytics (10.22.0):
    - FirebaseAnalytics/AdIdSupport (= 10.22.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - FirebaseCore (10.22.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreInternal (10.29.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseInstallations (10.29.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.22.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - Flutter (1.0.0)
  - flutter_facebook_auth (7.0.1):
    - FBSDKLoginKit (~> 17.0.2)
    - Flutter
  - flutter_inappwebview (0.0.1):
    - Flutter
    - flutter_inappwebview/Core (= 0.0.1)
    - OrderedSet (~> 5.0)
  - flutter_inappwebview/Core (0.0.1):
    - Flutter
    - OrderedSet (~> 5.0)
  - flutter_local_notifications (0.0.1):
    - Flutter
  - flutter_secure_storage (6.0.0):
    - Flutter
  - google_sign_in_ios (0.0.1):
    - Flutter
    - FlutterMacOS
    - GoogleSignIn (~> 7.0)
  - GoogleAppMeasurement (10.22.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.22.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.22.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.22.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30911.0, >= 2.30908.0)
  - GoogleDataTransport (9.4.1):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30911.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleSignIn (7.1.0):
    - AppAuth (< 2.0, >= 1.7.3)
    - GTMAppAuth (< 5.0, >= 4.1.1)
    - GTMSessionFetcher/Core (~> 3.3)
  - GoogleUtilities/AppDelegateSwizzler (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
    - GoogleUtilities/Privacy
  - GoogleUtilities/Environment (7.13.3):
    - GoogleUtilities/Privacy
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.13.3):
    - GoogleUtilities/Environment
    - GoogleUtilities/Privacy
  - GoogleUtilities/MethodSwizzler (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/Network (7.13.3):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Privacy
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.13.3)":
    - GoogleUtilities/Privacy
  - GoogleUtilities/Privacy (7.13.3)
  - GoogleUtilities/Reachability (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GoogleUtilities/UserDefaults (7.13.3):
    - GoogleUtilities/Logger
    - GoogleUtilities/Privacy
  - GTMAppAuth (4.1.1):
    - AppAuth/Core (~> 1.7)
    - GTMSessionFetcher/Core (< 4.0, >= 3.3)
  - GTMSessionFetcher/Core (3.5.0)
  - HydraAsync (2.0.6)
  - image_gallery_saver (2.0.2):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
  - libOpenInstallSDK_global (1.0.0)
  - MTBBarcodeScanner (5.0.11)
  - nanopb (2.30910.0):
    - nanopb/decode (= 2.30910.0)
    - nanopb/encode (= 2.30910.0)
  - nanopb/decode (2.30910.0)
  - nanopb/encode (2.30910.0)
  - openinstall_flutter_global (0.0.1):
    - Flutter
    - libOpenInstallSDK_global
  - OrderedSet (5.0.0)
  - package_info_plus (0.4.5):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.3.0):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - PromisesObjC (2.4.0)
  - ReachabilitySwift (5.2.4)
  - sensors_plus (0.0.1):
    - Flutter
  - share_plus (0.0.1):
    - Flutter
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - SwiftProtobuf (1.30.0)
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.5.6864)
  - TXIMSDK_Plus_iOS_XCFramework (8.5.6864)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_compress (0.3.0):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - adjust_sdk (from `.symlinks/plugins/adjust_sdk/ios`)
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - barcode_scan2 (from `.symlinks/plugins/barcode_scan2/ios`)
  - camera_avfoundation (from `.symlinks/plugins/camera_avfoundation/ios`)
  - connectivity_plus (from `.symlinks/plugins/connectivity_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - firebase_analytics (from `.symlinks/plugins/firebase_analytics/ios`)
  - firebase_core (from `.symlinks/plugins/firebase_core/ios`)
  - firebase_messaging (from `.symlinks/plugins/firebase_messaging/ios`)
  - Flutter (from `Flutter`)
  - flutter_facebook_auth (from `.symlinks/plugins/flutter_facebook_auth/ios`)
  - flutter_inappwebview (from `.symlinks/plugins/flutter_inappwebview/ios`)
  - flutter_local_notifications (from `.symlinks/plugins/flutter_local_notifications/ios`)
  - flutter_secure_storage (from `.symlinks/plugins/flutter_secure_storage/ios`)
  - google_sign_in_ios (from `.symlinks/plugins/google_sign_in_ios/darwin`)
  - image_gallery_saver (from `.symlinks/plugins/image_gallery_saver/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/ios`)
  - libOpenInstallSDK_global
  - openinstall_flutter_global (from `.symlinks/plugins/openinstall_flutter_global/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - sensors_plus (from `.symlinks/plugins/sensors_plus/ios`)
  - share_plus (from `.symlinks/plugins/share_plus/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite (from `.symlinks/plugins/sqflite/darwin`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_compress (from `.symlinks/plugins/video_compress/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Adjust
    - AdjustSignature
    - AppAuth
    - FBAEMKit
    - FBSDKCoreKit
    - FBSDKCoreKit_Basics
    - FBSDKLoginKit
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreInternal
    - FirebaseInstallations
    - FirebaseMessaging
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleSignIn
    - GoogleUtilities
    - GTMAppAuth
    - GTMSessionFetcher
    - HydraAsync
    - libOpenInstallSDK_global
    - MTBBarcodeScanner
    - nanopb
    - OrderedSet
    - PromisesObjC
    - ReachabilitySwift
    - SwiftProtobuf
    - TXIMSDK_Plus_iOS_XCFramework

EXTERNAL SOURCES:
  adjust_sdk:
    :path: ".symlinks/plugins/adjust_sdk/ios"
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  barcode_scan2:
    :path: ".symlinks/plugins/barcode_scan2/ios"
  camera_avfoundation:
    :path: ".symlinks/plugins/camera_avfoundation/ios"
  connectivity_plus:
    :path: ".symlinks/plugins/connectivity_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  firebase_analytics:
    :path: ".symlinks/plugins/firebase_analytics/ios"
  firebase_core:
    :path: ".symlinks/plugins/firebase_core/ios"
  firebase_messaging:
    :path: ".symlinks/plugins/firebase_messaging/ios"
  Flutter:
    :path: Flutter
  flutter_facebook_auth:
    :path: ".symlinks/plugins/flutter_facebook_auth/ios"
  flutter_inappwebview:
    :path: ".symlinks/plugins/flutter_inappwebview/ios"
  flutter_local_notifications:
    :path: ".symlinks/plugins/flutter_local_notifications/ios"
  flutter_secure_storage:
    :path: ".symlinks/plugins/flutter_secure_storage/ios"
  google_sign_in_ios:
    :path: ".symlinks/plugins/google_sign_in_ios/darwin"
  image_gallery_saver:
    :path: ".symlinks/plugins/image_gallery_saver/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/ios"
  openinstall_flutter_global:
    :path: ".symlinks/plugins/openinstall_flutter_global/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  sensors_plus:
    :path: ".symlinks/plugins/sensors_plus/ios"
  share_plus:
    :path: ".symlinks/plugins/share_plus/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite:
    :path: ".symlinks/plugins/sqflite/darwin"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_compress:
    :path: ".symlinks/plugins/video_compress/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  Adjust: 1ff981e84ae1306f8cce30e0661b3997dad1b85c
  adjust_sdk: 38843451cc76bacb2cf583a4e8c6e4052357dde2
  AdjustSignature: d634fc6b66295c38807f3b4e50978c1f72355950
  AppAuth: d4f13a8fe0baf391b2108511793e4b479691fb73
  audio_session: f08db0697111ac84ba46191b55488c0563bb29c6
  barcode_scan2: f80517f040989095c9b5067be77649bf6114442c
  camera_avfoundation: 26d4f51b8e0bb0443a1cfc08f1b58989e5c39f11
  connectivity_plus: 8443422d4c5a53dee0d50779ec5dbcda1071251e
  device_info_plus: 335f3ce08d2e174b9fdc3db3db0f4e3b1f66bd89
  FBAEMKit: 9900b2edd99a2d21629a6277e6166f14c6215799
  FBSDKCoreKit: 0791f8f68a8630931a4c12aa23a56cc021551596
  FBSDKCoreKit_Basics: 46d6b472c0dd0a5a7e972c025033d1c567f54eb4
  FBSDKLoginKit: b4a4eba1d62eb452544411824f41689adabd5bd2
  Firebase: 797fd7297b7e1be954432743a0b3f90038e45a71
  firebase_analytics: 70651a3039e73e5878dd5bb832ac9154c30b5bb5
  firebase_core: 2a80983e98480c7f02ff001358621c8d7f1ff8f0
  firebase_messaging: 879b396d2be7aa1eaf044d50d2407d24c971b934
  FirebaseAnalytics: 8d0ff929c63b7f72260f332b86ccf569776b75d3
  FirebaseCore: 0326ec9b05fbed8f8716cddbf0e36894a13837f7
  FirebaseCoreInternal: df84dd300b561c27d5571684f389bf60b0a5c934
  FirebaseInstallations: 913cf60d0400ebd5d6b63a28b290372ab44590dd
  FirebaseMessaging: 9f71037fd9db3376a4caa54e5a3949d1027b4b6e
  Flutter: f04841e97a9d0b0a8025694d0796dd46242b2854
  flutter_facebook_auth: 8ae86c1cf564cad8bbdead592c9ebb4e300f1e3b
  flutter_inappwebview: 0234ff1dc6dbc326c2d506d634757d5166675113
  flutter_local_notifications: ad39620c743ea4c15127860f4b5641649a988100
  flutter_secure_storage: 1ed9476fba7e7a782b22888f956cce43e2c62f13
  google_sign_in_ios: 47da87bbec3b945887fa0161aac6d9a0a9bd0c82
  GoogleAppMeasurement: ccefe3eac9b0aa27f96066809fb1a7fe4b462626
  GoogleDataTransport: 6c09b596d841063d76d4288cc2d2f42cc36e1e2a
  GoogleSignIn: d4281ab6cf21542b1cfaff85c191f230b399d2db
  GoogleUtilities: ea963c370a38a8069cc5f7ba4ca849a60b6d7d15
  GTMAppAuth: f69bd07d68cd3b766125f7e072c45d7340dea0de
  GTMSessionFetcher: 5aea5ba6bd522a239e236100971f10cb71b96ab6
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_gallery_saver: 14711d79da40581063e8842a11acf1969d781ed7
  just_audio: 6c031bb61297cf218b4462be616638e81c058e97
  libOpenInstallSDK_global: 4f9a42c394e223f51b518fde272588aa69c1a3ba
  MTBBarcodeScanner: f453b33c4b7dfe545d8c6484ed744d55671788cb
  nanopb: 438bc412db1928dac798aa6fd75726007be04262
  openinstall_flutter_global: 47b59990ea297f93c356f2e9e655f0cacb65390d
  OrderedSet: aaeb196f7fef5a9edf55d89760da9176ad40b93c
  package_info_plus: 566e1b7a2f3900e4b0020914ad3fc051dcc95596
  path_provider_foundation: 608fcb11be570ce83519b076ab6a1fffe2474f05
  permission_handler_apple: 4ed2196e43d0651e8ff7ca3483a069d469701f2d
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  PromisesObjC: f5707f49cb48b9636751c5b2e7d227e43fba9f47
  ReachabilitySwift: 32793e867593cfc1177f5d16491e3a197d2fccda
  sensors_plus: 1c5f0a01ce21c609a4df404c4e6879d62bce287f
  share_plus: de6030e33b4e106470e09322d87cf2a4258d2d1d
  shared_preferences_foundation: 0b09b969fb36da5551c0bc4a2dbd9d0ff9387478
  sqflite: c35dad70033b8862124f8337cc994a809fcd9fa3
  SwiftProtobuf: 3697407f0d5b23bedeba9c2eaaf3ec6fdff69349
  tencent_cloud_chat_sdk: f60b13e08e8aa0c1efb4e1929e100fdba0f4c7b1
  TXIMSDK_Plus_iOS_XCFramework: 0353712b504d2206ce0f4d94b0eb3357673e1cfe
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_compress: f2133a07762889d67f0711ac831faa26f956980e
  video_player_avfoundation: 5685dd1957b7437a39376959f3502aac60b47aa5
  wakelock_plus: 8c239121a007daa1d6759c6acdc507860273dd2f
  webview_flutter_wkwebview: daa94b5ed120e19439eb7f797649768d6360ebdd

PODFILE CHECKSUM: ddb77de8366c427e02b136c99ae5e4fec6d5a649

COCOAPODS: 1.16.2
