import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api_hrx.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';

import '../../../utils/app_utils.dart';
import '../../../utils/custom_sp_util.dart';


class ReqSquare {


  ///
  /// 获取广场列表
  ///
  static Future<ApiResponse<dynamic>> getSquares(int p) async {
    try {
      var response = await HttpUtils.post(ApiHrx.GetSquareList, data: {
        'uid': CustomSpUtil.getUid(),
        'p': p,
        'isstart': 0,
        'mobileid': await AppUtils.getDeviceId(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}