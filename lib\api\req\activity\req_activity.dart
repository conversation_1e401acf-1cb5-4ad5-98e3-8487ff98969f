import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqActivity {

  /// [activityid] 活动id
  /// [keyword] 关键字
  /// [page] 页数
  /// [type] 类型,0=正常,1=取前三
  ///
  static Future<ApiResponse<dynamic>> getRankingList({
    int? activityid,
    String? keyword,
    int page = 1,
    int type = 0
  }) async {
    try {
      var response = await HttpUtils.post(Api.getRankingList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'activityid': activityid,
        'keyword': keyword,
        'p': page,
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取活动详细信息
  ///
  static Future<ApiResponse<dynamic>> getActivityInfo() async {
    try {
      var response = await HttpUtils.post(Api.getActivityInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 创建战队
  /// [activityid] 活动id
  /// [name] 战队名称
  ///
  static Future<ApiResponse<dynamic>> setRoom({
    int? activityid,
    String? name,
  }) async {
    try {
      var response = await HttpUtils.post(Api.setRoom, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'activityid': activityid,
        'name': name
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 加入战队
  /// [roomid] 战队id
  ///
  static Future<ApiResponse<dynamic>> joinRoom({
    String? roomid,
  }) async {
    try {
      var response = await HttpUtils.post(Api.joinRoom, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'roomid': roomid,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的战队排行榜
  /// [roomid] 战队id
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> getMyRankingList({
    String? roomid,
    int page = 1,
  }) async {
    try {
      var response = await HttpUtils.post(Api.getMyRankingList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'roomid': roomid,
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的战队信息
  /// [activityid] 活动id
  ///
  static Future<ApiResponse<dynamic>> getMyRoomInfo({
    String? activityid,
  }) async {
    try {
      var response = await HttpUtils.post(Api.getMyRoomInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'activityid': activityid,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}