import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqAuth {
  ///
  /// 用户提交认证信息
  /// [realName] 真实姓名
  /// [cerNo] 身份证号
  /// [frontView] 证件正面
  /// [backView] 证件反面
  /// [handsetView] 手持证件照
  /// [nation] 国家
  /// [type] 类型,0:身份证,1:护照,2:驾照
  ///
  static Future<ApiResponse<dynamic>> setAuth(
      {String? realName,
      String? cerNo,
      String? countryCode,
      String? mobile,
      String? frontView,
      String? backView,
      String? handsetView,
      String? nation,
      String? type}) async {
    try {
      var response = await HttpUtils.post(Api.setAuth, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'real_name': realName,
        'country_code': countryCode,
        'mobile': mobile,
        'cer_no': cerNo,
        'front_view': frontView,
        'back_view': backView,
        'handset_view': handsetView,
        'nation': nation,
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取云存储方式、获取七牛上传验证token字符串、获取腾讯云存储相关配置信息、获取亚马逊存储相关配置信息
  ///
  static Future<ApiResponse<dynamic>> getCosInfo() async {
    try {
      var response = await HttpUtils.post(Api.getCosInfo);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户认证信息
  ///
  static Future<ApiResponse<dynamic>> getAuth() async {
    try {
      var response = await HttpUtils.post(Api.getAuth, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }
}
