import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqContract {

  ///
  /// 获取详细信息
  ///
  static Future<ApiResponse<dynamic>> contractGetInfo() async {
    try {
      var response = await HttpUtils.post(Api.contractGetInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取K线列表
  /// [period] 周期,支持5min=5分钟,15min=15分钟,30min=30分钟,12hour=12小时,1day=1日,1week=1周,1month=1月
  ///
  static Future<ApiResponse<dynamic>> contractGetList({
    String period = '5min',
  }) async {
    try {
      var response = await HttpUtils.post(Api.contractGetList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'period': period
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}