import 'package:dio/dio.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';

import '../../../utils/custom_sp_util.dart';
import '../../api.dart';
import '../../api_response.dart';
import '../../http_utils.dart';

class ReqSend {
  ///
  /// 获取弹幕列表
  ///
  static getDanmakuList() async {
    logger.d('获取弹幕列表');
    try {
      var response = await HttpUtils.post(Api.getDanmakuList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      logger.d('获取弹幕列表失败${e.message}');
      return ApiResponse.error(e.error);
    }
  }
}
