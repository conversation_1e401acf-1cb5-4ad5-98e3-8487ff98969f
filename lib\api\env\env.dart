

// 获取的配置信息
import 'package:flutter_starry_sky_box/api/env/config/dev_config.dart';
import 'package:flutter_starry_sky_box/api/env/config/local_config.dart';
import 'package:flutter_starry_sky_box/api/env/config/pro_config.dart';
import 'package:flutter_starry_sky_box/api/env/env_config.dart';
import 'package:flutter_starry_sky_box/api/env/env_name.dart';

class Env {
  // 获取到当前环境
  static const appEnv = String.fromEnvironment(EnvName.envKey);

  // 开发环境
  static final EnvConfig _devConfig = DevConfig.config;
  // 生产环境
  static final EnvConfig _proConfig = ProConfig.config;
  // 本地环境
  static final EnvConfig _localConfig = LocalConfig.config;

  static EnvConfig get envConfig => _getEnvConfig();

  // 根据不同环境返回对应的环境配置
  static EnvConfig _getEnvConfig() {
    switch (appEnv) {
      case EnvName.dev:
        return _devConfig;
      case EnvName.pro:
        return _proConfig;
      case EnvName.local:
        return _localConfig;
      default:
        return _devConfig;
    }
  }
}