import UIKit
import Flutter
// import openinstall_flutter_global

@main
@objc class AppDelegate: FlutterAppDelegate {
  override func application(
    _ application: UIApplication,
    didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?
  ) -> Bool {
    GeneratedPluginRegistrant.register(with: self)
//     OpenInstallSDK.initWith(self);
    return super.application(application, didFinishLaunchingWithOptions: launchOptions)
  }
}
