import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqSystem {

  ///
  /// 官方通知列表（官方公告）
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> officialLists(int page) async {
    try {
      var response = await HttpUtils.post(Api.officialLists, data: {
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 系统通知列表（公司新闻）
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> systemnotifyLists(int page) async {
    try {
      var response = await HttpUtils.post(Api.systemnotifyLists, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 文章列表（帮助中心）
  ///
  static Future<ApiResponse<dynamic>> getArticle() async {
    try {
      var response = await HttpUtils.post(Api.getArticle);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取铭文列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> goodsGetList(int page) async {
    try {
      var response = await HttpUtils.post(Api.goodsGetList, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 购买铭文
  /// [goodsId] 铭文ID
  ///
  static Future<ApiResponse<dynamic>> buyGoods(int goodsId) async {
    try {
      var response = await HttpUtils.post(Api.buyGoods, data: {
        'goods_id': goodsId,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}