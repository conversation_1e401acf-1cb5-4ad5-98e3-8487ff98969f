import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';

import '../../../utils/custom_sp_util.dart';

class ReqHome {

  ///
  /// 获取通知公告信息
  ///
  static Future<ApiResponse<dynamic>> homeGetNotify() async {
    try {
      var response = await HttpUtils.post(Api.getNotify, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    }
  }
  /// 获取通知等级信息
  ///
  static Future<ApiResponse<dynamic>> homeGetLevelNotify() async {
    try {
      var response = await HttpUtils.post(Api.getLevelNotify, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    }
  }
  ///
  /// 获取配置信息
  ///
  static Future<ApiResponse<dynamic>> homeGetConfig() async {
    try {
      var response = await HttpUtils.get(Api.homeGetConfig);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 文章详情
  /// [type] 类型,166:秘豆收益说明,167:星级规则说明,168:邀请说明,169:铭文规则说明
  ///
  static Future<ApiResponse<dynamic>> getArticleDetail(String type) async {
    try {
      var response = await HttpUtils.post(Api.getArticleDetail, data: {
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 平台币信息
  ///
  static Future<ApiResponse<dynamic>> homeGetPlatformCoin() async {
    try {
      var response = await HttpUtils.post(Api.homeGetPlatformCoin);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}