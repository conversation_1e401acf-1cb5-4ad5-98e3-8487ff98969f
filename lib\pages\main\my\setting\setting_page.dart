import 'package:flustars_flutter3/flustars_flutter3.dart';
import 'package:flutter/material.dart';
import 'package:flutter_starry_sky_box/pages/main/my/user_store.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/routers/app_routes.dart';
import 'package:flutter_starry_sky_box/utils/cache_manager.dart';
import 'package:flutter_starry_sky_box/utils/crash_logger.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:flutter_starry_sky_box/utils/tencent_utils.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_starry_sky_box/widget/sheet/exit_tip_sheet.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../../utils/app_utils.dart';
import '../../../../utils/custom_constant.dart';
import '../../../../utils/custom_event_util.dart';
import '../../main_controller.dart';

/// 设置
class SettingPage extends StatefulWidget {
  const SettingPage({super.key});

  @override
  _SettingPageState createState() => _SettingPageState();
}

class _SettingPageState extends State<SettingPage> {
  // late StreamSubscription _subscription;

  @override
  void initState() {
    super.initState();
    // // 注册事件监听器
    eventBus.on<BottomNavigationBarEvent>().listen((event) {
      if (event.message == CustomConstant.LOGIN_REFRESH) {
        Get.back();
        MainController.to.jumpToPage(0);
      }
    });
  }

  @override
  void dispose() {
    // 取消事件监听
    // _subscription.cancel();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RC.white,
      appBar: AppBar(
        backgroundColor: RC.white,
        leading: IconButton(
          onPressed: () {
            Get.back();
          },
          icon: SvgPicture.asset('assets/images/svg/message/back.svg'),
        ),
        title: Text(
          'setting'.tr,
          style: Styles.font_custom(
            fontSize: 17.sp,
            fontWeight: FontWeight.w500,
            color: RC.color1A1A1A,
          ),
        ),
        centerTitle: true,
      ),
      body: SingleChildScrollView(
        padding: EdgeInsets.symmetric(horizontal: 15.w),
        child: Column(
          children: [
            _buildItem('language'.tr, onTap: () {
              Get.toNamed(AppRoutes.language);
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('check_for_updates'.tr, onTap: () {
              UserStore.to.checkUpdate();
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('clear_cache'.tr, onTap: () async {
              String size = await CacheManager.getFormattedCacheSize();
              ExitTipSheet.show(
                title: 'cache_size_confirm_clearing_cache'
                    .trParams({'size': '$size'}),
                onConfirm: () {
                  CacheManager.clearAllCache();
                  Get.back();
                },
              );
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('about_text'.tr, onTap: () {
              Get.toNamed(AppRoutes.aboutUs);
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('异常日志', onTap: () {
              Get.toNamed(AppRoutes.crashLogs);
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('测试异常', onTap: () async {
              // 测试异常捕获功能
              CrashLogger.addTestLog();

              // 获取调试信息
              final filePath = await CrashLogger.getLogFilePath();
              final fileExists = await CrashLogger.logFileExists();
              final logs = await CrashLogger.instance.getAllLogs();

              // 显示详细信息
              if (mounted) {
                showDialog(
                  context: context,
                  builder: (context) => AlertDialog(
                    title: const Text('调试信息'),
                    content: Column(
                      mainAxisSize: MainAxisSize.min,
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text('日志文件路径: $filePath'),
                        Text('文件是否存在: $fileExists'),
                        Text('日志数量: ${logs.length}'),
                        if (logs.isNotEmpty) Text('最新日志: ${logs.first.error}'),
                      ],
                    ),
                    actions: [
                      TextButton(
                        onPressed: () => Navigator.of(context).pop(),
                        child: const Text('确定'),
                      ),
                    ],
                  ),
                );
              }
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
            _buildItem('logout'.tr, onTap: () async {
              ExitTipSheet.show(
                color: RC.colorEB434B,
                title: 'confirm_logging_out'.tr,
                onConfirm: () {
                  AppUtils.logout();
                  Get.back();
                  Get.offAllNamed(AppRoutes.login);
                },
              );
            }),
            Container(
              width: double.infinity,
              height: 1.w,
              color: RC.colorEFEFEF,
            ),
          ],
        ),
      ),
    );
  }

  _buildItem(String leftText, {Function? onTap}) {
    return GestureDetector(
      behavior: HitTestBehavior.translucent,
      onTap: () {
        onTap?.call();
      },
      child: Container(
        padding: EdgeInsets.all(15.w),
        child: Row(
          children: [
            Text(
              leftText,
              style: TextStyle(
                fontSize: 14.5.sp,
                fontWeight: FontWeight.w400,
                color: RC.black,
              ),
            ),
            const Spacer(),
            SizedBox(
              child: SvgPicture.asset(
                'assets/images/svg/my/my_right_arrow.svg',
                color: RC.color222222,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
