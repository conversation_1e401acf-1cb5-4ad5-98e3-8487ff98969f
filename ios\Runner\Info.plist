<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleDevelopmentRegion</key>
	<string>$(DEVELOPMENT_LANGUAGE)</string>
	<key>CFBundleDisplayName</key>
	<string>FunShot</string>
	<key>CFBundleExecutable</key>
	<string>$(EXECUTABLE_NAME)</string>
	<key>CFBundleIdentifier</key>
	<string>$(PRODUCT_BUNDLE_IDENTIFIER)</string>
	<key>CFBundleInfoDictionaryVersion</key>
	<string>6.0</string>
	<key>CFBundleName</key>
	<string>FunShot</string>
	<key>CFBundlePackageType</key>
	<string>APPL</string>
	<key>CFBundleShortVersionString</key>
	<string>1.0.0</string>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>com.googleusercontent.apps.879269719455-61v6f8qorv9f9ijlac8ffq1229gvtval</string>
				<string>fb1976088946519062</string>
				<string>l23zgl</string>
			</array>
		</dict>
	</array>
	<key>CFBundleVersion</key>
	<string>1</string>
	<key>FacebookAppID</key>
	<string>1976088946519062</string>
	<key>FacebookClientToken</key>
	<string>********************************</string>
	<key>FacebookDisplayName</key>
	<string>FunShot</string>
	<key>GIDClientID</key>
	<string>879269719455-61v6f8qorv9f9ijlac8ffq1229gvtval.apps.googleusercontent.com</string>
	<key>ITSAppUsesNonExemptEncryption</key>
	<false/>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>fbapi</string>
		<string>fb-messenger-share-api</string>
	</array>
	<key>LSRequiresIPhoneOS</key>
	<true/>
	<key>LSSupportsOpeningDocumentsInPlace</key>
	<true/>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<true/>
		<key>NSAllowsLocalNetworking</key>
		<true/>
	</dict>
	<key>NSAppleMusicUsageDescription</key>
	<string>FunShot需要音乐库权限，应用需要访问您的音乐库以提供更好的音乐播放体验</string>
	<key>NSBluetoothAlwaysUsageDescription</key>
	<string>FunShot需要访问您的蓝牙，用于ai通话时候可以连接蓝牙设备</string>
	<key>NSBluetoothPeripheralUsageDescription</key>
	<string>FunShot需要访问您的蓝牙，用于ai通话时候可以连接蓝牙设备</string>
	<key>NSCalendarsUsageDescription</key>
	<string>FunShot需要访问您的日历，以便提醒您重要的事件和活动。</string>
	<key>NSCameraUsageDescription</key>
	<string>FunShot需要您的相机权限，才能进行拍照更改头像以及上传图片和视频</string>
	<key>NSContactsUsageDescription</key>
	<string>FunShot需要访问您的联系人，用于给您提供更好的帮助</string>
	<key>NSLocationAlwaysAndWhenInUseUsageDescription</key>
	<string>FunShot需要访问您的后台和前台获取位置信息，才能给您提供附近商家信息</string>
	<key>NSLocationWhenInUseUsageDescription</key>
	<string>允许FunShot在您使用应用时获取您的位置信息，以便提供附近的商家信息</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>FunShot需要麦克风权限，才有获取您的录音，用于与AI通话，请允许APP使用此权限</string>
	<key>NSMotionUsageDescription</key>
	<string>为了提供准确的步数统计和健康建议，FunShot需要访问您的运动与健身数据</string>
	<key>NSPhotoLibraryAddUsageDescription</key>
	<string>FunShot需要访问相册和存储照片到相册，请允许APP保存图片到相册</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>FunShot需要您的相册权限，才能选择照片更改头像以及上传图片和视频</string>
	<key>NSSiriUsageDescription</key>
	<string>FunShot需要访问您的Siri，用于快速设置闹钟或提醒</string>
	<key>NSSpeechRecognitionUsageDescription</key>
	<string>FunShot使用语音识别技术，允许用户口述笔记或搜索查询</string>
	<key>SKAdNetworkItems</key>
	<array/>
	<key>UIApplicationSupportsIndirectInputEvents</key>
	<true/>
	<key>UIBackgroundModes</key>
	<array>
		<string>remote-notification</string>
		<string>fetch</string>
	</array>
	<key>UILaunchStoryboardName</key>
	<string>LaunchScreen</string>
	<key>UIMainStoryboardFile</key>
	<string>Main</string>
	<key>UISupportedInterfaceOrientations</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>UISupportedInterfaceOrientations~ipad</key>
	<array>
		<string>UIInterfaceOrientationPortrait</string>
		<string>UIInterfaceOrientationPortraitUpsideDown</string>
		<string>UIInterfaceOrientationLandscapeLeft</string>
		<string>UIInterfaceOrientationLandscapeRight</string>
	</array>
	<key>com.openinstall.APP_KEY</key>
	<string>l23zgl</string>
</dict>
</plist>
