import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqMessage {
  ///
  /// 获取私聊未查看消息列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> messgaeGetChatList(int page) async {
    try {
      var response = await HttpUtils.post(Api.messgaeGetChatList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取私聊消息列表
  /// [page] 页数
  /// [touid] 对方UID
  ///
  static Future<ApiResponse<dynamic>> messageGetChat(int page,
      {int? touid}) async {
    try {
      var response = await HttpUtils.post(Api.messageGetChat, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'touid': touid,
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 私聊发送消息
  /// [touid] 对方ID
  /// [content] 内容
  ///
  static Future<ApiResponse<dynamic>> messageSetChat(
      {int? touid, String? content}) async {
    try {
      var response = await HttpUtils.post(Api.messageSetChat, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'touid': touid,
        'content': content,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取未读数
  ///
  static Future<ApiResponse<dynamic>> getNoticeNum() async {
    try {
      var response = await HttpUtils.post(Api.getNoticeNum, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取系统通知详情
  /// [id] 系统通知id
  ///
  static Future<ApiResponse<dynamic>> systemnotifyDetails(int id) async {
    try {
      var response = await HttpUtils.post(Api.systemnotifyDetails, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'id': id
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 私聊删除消息
  /// [touid] 对方ID
  ///
  static Future<ApiResponse<dynamic>> messageDelChat(int touid) async {
    try {
      var response = await HttpUtils.post(Api.messageDelChat, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'touid': touid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取评论信息列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> messageCommentLists(int page) async {
    try {
      var response = await HttpUtils.post(Api.messageCommentLists, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取视频评论点赞列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> videoCommentLikeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.videoCommentLikeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取提示用户消息列表
  ///
  static Future<ApiResponse<dynamic>> getUserTipsList(int page) async {
    try {
      var response = await HttpUtils.post(Api.getUserTipsList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取提示用户消息详情并改已读
  ///
  static Future<ApiResponse<dynamic>> getUserTipsDetails(String id) async {
    try {
      var response = await HttpUtils.post(Api.getUserTipsDetails, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'id': id,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 一键已读
  ///
  static Future<ApiResponse<dynamic>> messageReadAll() async {
    try {
      var response = await HttpUtils.post(Api.messageReadAll, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取视频点赞列表
  ///
  static Future<ApiResponse<dynamic>> videoLikeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.videoLikeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取官方通知详情
  ///
  static Future<ApiResponse<dynamic>> officialDetails(int id) async {
    try {
      var response = await HttpUtils.post(Api.officialDetails,
          data: {'uid': CustomSpUtil.getUid(), 'id': id});
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 团队改已读
  ///
  static Future<ApiResponse<dynamic>> messageTeamRead() async {
    try {
      var response = await HttpUtils.post(Api.messageTeamRead, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 赠送铭文改已读
  ///
  static Future<ApiResponse<dynamic>> messageGoodsRead() async {
    try {
      var response = await HttpUtils.post(Api.messageGoodsRead, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取评论和点赞未读数
  ///
  static Future<ApiResponse<dynamic>> commentLikeInfo() async {
    try {
      var response = await HttpUtils.post(Api.commentLikeInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 关注改已读
  ///
  static Future<ApiResponse<dynamic>> messageFollowRead() async {
    try {
      var response = await HttpUtils.post(Api.messageFollowRead, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }
}
