import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqPlaza {

  ///
  /// 获取广场列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> plazaGetList(int page) async {
    try {
      var response = await HttpUtils.post(Api.plazaGetList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取个人主页广场
  /// [page] 页数
  /// [touid] 对方ID
  ///
  static Future<ApiResponse<dynamic>> plazaGetHomePlaza(int page, {
    int? touid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaGetHomePlaza, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'touid': touid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取广场评论列表
  /// [page] 页数
  /// [plazaid] 广场ID
  ///
  static Future<ApiResponse<dynamic>> plazaGetComments(int page, {
    int? plazaid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaGetComments, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'plazaid': plazaid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 广场点赞数累计
  /// [plazaid] 广场ID
  ///
  static Future<ApiResponse<dynamic>> plazaAddLike({
    int? plazaid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaAddLike, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'plazaid': plazaid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 评论/回复的点赞数累计
  /// [commentid] 评论/回复 ID
  ///
  static Future<ApiResponse<dynamic>> plazaAddCommentLike({
    int? commentid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaAddCommentLike, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'commentid': commentid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户评论广场/回复别人评论
  /// [plazaid] 广场ID
  /// [content] 内容
  ///
  static Future<ApiResponse<dynamic>> plazaSetComment({
    String? plazaid,
    String? content,

    String? commentid,
    String? parentid,
    String? touid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaSetComment, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'plazaid': plazaid,
        'content': content,
        'touid': touid,
        'commentid': commentid,
        'parentid': parentid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 广场删除评论以及子级评论
  /// [plazaid] 广场ID
  /// [commentid] 评论ID
  /// [commentuid] 评论者用户ID
  ///
  static Future<ApiResponse<dynamic>> plazaDelComments({
    String? plazaid,
    String? commentid,
    String? commentuid
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaDelComments, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'plazaid': plazaid,
        'commentid': commentid,
        'commentuid': commentuid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 广场搜索
  /// [page] 页数
  /// [key] 关键词
  ///
  static Future<ApiResponse<dynamic>> plazaSearch({
    int? page,
    String? key
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaSearch, data: {
        'uid': CustomSpUtil.getUid(),
        'p': page,
        'key': key,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取关注的人发布的广场
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> getAttentionPlaza(int page) async {
    try {
      var response = await HttpUtils.post(Api.getAttentionPlaza, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 发布广场信息
  /// [images] 图片集
  /// [content] 内容
  /// [ishide] 私密,0否,1是
  ///
  static Future<ApiResponse<dynamic>> plazaSetPlaza(String content, {
    int ishide = 0,
    String? images,
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaSetPlaza, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'content': content,
        'images': images,
        'ishide': ishide
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 删除广场以及相关信息
  /// [plazaid] 广场ID
  ///
  static Future<ApiResponse<dynamic>> plazaDel({
    int plazaid = 0,
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaDel, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'plazaid': plazaid,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 设置\取消私密
  /// [plazaid] 广场ID
  /// [status] 状态,0=取消,1=设置私密
  ///
  static Future<ApiResponse<dynamic>> plazaHide({
    int plazaid = 0,
    int? status
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaHide, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'plazaid': plazaid,
        'status': status
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }


  ///
  /// 获取个人主页广场
  /// [page] 页数
  /// [type] 状态,1=公开,2=私密
  ///
  static Future<ApiResponse<dynamic>> plazaGetMyPlaza(int page, {
    int? type
  }) async {
    try {
      var response = await HttpUtils.post(Api.plazaGetMyPlaza, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取评论信息列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> plazaCommentLists(int page) async {
    try {
      var response = await HttpUtils.post(Api.plazaCommentLists, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取广场评论点赞列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> plazaCommentLikeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.plazaCommentLikeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取广场点赞列表
  ///
  static Future<ApiResponse<dynamic>> plazaLikeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.plazaLikeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }


}