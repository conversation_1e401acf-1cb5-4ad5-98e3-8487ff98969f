import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_hrx.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';

import '../../../utils/custom_sp_util.dart';


class ReqTask {

  ///
  /// 获取用户信息（用于获取单个用户基本信息,注:返回增加level:等级 dou_coin:秘豆,watchtime:累计视频时长）
  ///
  static Future<ApiResponse<dynamic>> getBaseInfo() async {
    try {
      var response = await HttpUtils.post(ApiHrx.userGetBaseInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户签到奖励信息
  ///
  static Future<ApiResponse<dynamic>> getBonusInfo() async {
    try {
      var response = await HttpUtils.post(Api.userBonus, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户查看每日任务的进度
  ///
  static Future<ApiResponse<dynamic>> seeDailyTasks() async {
    try {
      var response = await HttpUtils.post(Api.seeDailyTasks, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 领取每日任务奖励
  /// [taskid] 任务ID
  ///
  static Future<ApiResponse<dynamic>> receiveTaskReward(int taskid) async {
    try {
      var response = await HttpUtils.post(Api.receiveTaskReward, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'taskid': taskid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取铭文购买通知
  ///
  static Future<ApiResponse<dynamic>> goodsGetNotify() async {
    try {
      var response = await HttpUtils.post(Api.goodsGetNotify, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}