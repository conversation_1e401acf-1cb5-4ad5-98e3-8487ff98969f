import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqLevel {

  ///
  /// 获取等级列表
  ///
  static Future<ApiResponse<dynamic>> getLevelList() async {
    try {
      var response = await HttpUtils.post(Api.getLevelList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 升级等级
  ///
  static Future<ApiResponse<dynamic>> levelBuyLevel(int level) async {
    try {
      var response = await HttpUtils.post(Api.levelBuyLevel, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'level': level
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户当前等级信息
  ///
  static Future<ApiResponse<dynamic>> levelGetUserInfo() async {
    try {
      var response = await HttpUtils.post(Api.levelGetUserInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取活跃好友
  ///
  static Future<ApiResponse<dynamic>> levelGetUserList({
    String keyword = '',
    String type = '0',
    int? page 
  }) async {
    try {
      var response = await HttpUtils.post(Api.levelGetUserList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'type': type,
        'keyword': keyword
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }


}