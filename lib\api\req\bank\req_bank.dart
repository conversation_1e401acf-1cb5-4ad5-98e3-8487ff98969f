import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqBank {

  ///
  /// 用户提现账号
  ///
  static Future<ApiResponse<dynamic>> getAccountList() async {
    try {
      var response = await HttpUtils.post(Api.getAccountList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户添加提现账号
  /// [type] 账号类型，1支付宝，2微信，3银行卡
  /// [accountBank] 银行名称
  /// [account] account
  /// [name] 姓名
  ///
  static Future<ApiResponse<dynamic>> setAccount(
    {
      int? type,
      String? accountBank,
      String? account,
      String? name,
      String? added1
    }
  ) async {
    try {
      var response = await HttpUtils.post(Api.setAccount, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type,
        'account_bank': accountBank,
        'account': account,
        'name': name,
        'added1': added1
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户删除提现账号
  /// [id] 帐号id
  ///
  static Future<ApiResponse<dynamic>> delAccount(int id) async {
    try {
      var response = await HttpUtils.post(Api.delAccount, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'id': id
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取提现币种
  ///
  static Future<ApiResponse<dynamic>> getCurrency() async {
    try {
      var response = await HttpUtils.post(Api.getCurrency, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取印尼代付银行
  ///
  static Future<ApiResponse<dynamic>> cashGetBank() async {
    try {
      var response = await HttpUtils.post(Api.cashGetBank, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于获取印尼钱包代付
  ///
  static Future<ApiResponse<dynamic>> cashGetWallet() async {
    try {
      var response = await HttpUtils.post(Api.cashGetWallet, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}