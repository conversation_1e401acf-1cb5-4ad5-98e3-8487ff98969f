import 'package:dio/dio.dart';
import 'package:flustars_flutter3/flustars_flutter3.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/app_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_logger.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqUser {
  ///
  /// 用于获取国家列表
  ///
  static Future<ApiResponse<dynamic>> loginGetCountrys() async {
    try {
      var response = await HttpUtils.get(Api.loginGetCountrys);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 会员注册
  /// [type] 类型:phone=手机号,email=邮箱
  /// [countryCode] 国家区号
  /// [userLogin] 帐号
  /// [code] 验证码
  /// [password] 密码
  /// [agentCode] 邀请码
  /// [source] 来源
  /// [mobileId] 手机设备号
  ///
  static Future<ApiResponse<dynamic>> loginUserRegister(
      {required String type,
      String countryCode = '',
      required String userLogin,
      required String code,
      required String password,
      String agentCode = '',
      String utmSource = '',
      String fcm_token = '',
      String source = '',
      String mobileId = ''}) async {
    try {
      var response = await HttpUtils.post(Api.loginUserRegister, data: {
        'type': type,
        'country_code': countryCode,
        'user_login': userLogin,
        'code': code,
        'password': password,
        'fcm_token': fcm_token,
        'agentcode': agentCode,
        'utm_source': utmSource,
        'source': AppUtils.getSource(),
        'mobileid': await AppUtils.getDeviceId()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取登录邮箱验证码
  /// [email] 邮箱
  ///
  static Future<ApiResponse<dynamic>> loginGetEmailCode(
      {String email = '', String type = '0'}) async {
    try {
      var response = await HttpUtils.post(Api.loginGetEmailCode, data: {
        'email': email,
        'type': type,
        'time': AppUtils.getEmailSign(email).time,
        'sign': AppUtils.getEmailSign(email).sign
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取登录短信验证码
  /// [countryCode] 国家代号
  /// [mobile] 手机号
  ///
  static Future<ApiResponse<dynamic>> loginGetLoginCode(
      {String countryCode = '', String mobile = '', String type = '0'}) async {
    try {
      var response = await HttpUtils.post(Api.loginGetLoginCode, data: {
        'country_code': countryCode,
        'mobile': mobile,
        'fcm_token': SpUtil.getString("fcm_token", defValue: "null") ?? "null",
        'time': AppUtils.getMobileSign(countryCode, mobile).time,
        'sign': AppUtils.getMobileSign(countryCode, mobile).sign,
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 会员登录,新增(用于手机号和邮箱登陆)
  /// [loginType] 类型:pwd=密码,code=验证码
  /// [type] 类型:phone=手机号,email=邮箱
  /// [countryCode] 国家区号
  /// [userLogin] 账号
  /// [code] 密码/验证码
  ///
  static Future<ApiResponse<dynamic>> userLogin2(
      {String loginType = '',
      String type = '',
      String countryCode = '',
      String userLogin = '',
      String code = ''}) async {
    try {
      var data = {
        'login_type': loginType,
        'type': type,
        'country_code': countryCode,
        'user_login': userLogin,
        'code': code,
        'fcm_token': SpUtil.getString("fcm_token", defValue: "null") ?? "null",
        'source': AppUtils.getSource(),
        'mobileid': await AppUtils.getDeviceId()
      };
      var response = await HttpUtils.post(Api.userLogin2, data: data);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 忘记密码(用于找回,修改密码)
  /// [type] 类型:phone=手机号,email=邮箱
  /// [countryCode] 国家区号
  /// [userLogin] 账号
  /// [code] 验证码
  /// [password] 密码
  ///
  static Future<ApiResponse<dynamic>> userRetrieve(
      {String type = '',
      String countryCode = '',
      String userLogin = '',
      String code = '',
      String password = ''}) async {
    try {
      var response = await HttpUtils.post(Api.userRetrieve, data: {
        'type': type,
        'country_code': countryCode,
        'user_login': userLogin,
        'code': code,
        'password': password,
        'source': AppUtils.getSource(),
        'mobileid': await AppUtils.getDeviceId()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户信息（用于获取单个用户基本信息,注:返回增加level:等级 dou_coin:秘豆,watchtime:累计视频时长）
  ///
  static Future<ApiResponse<dynamic>> getBaseInfo() async {
    try {
      var response = await HttpUtils.post(Api.getBaseInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取铭文购买列表（铭文中心）
  /// [status] 0-我的任务 1-已完成
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> getLogList(
      {int status = -1, int page = 1}) async {
    try {
      var response = await HttpUtils.post(Api.getLogList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'status': status,
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的上级信息（我的上级）
  ///
  static Future<ApiResponse<dynamic>> myUpInfo() async {
    try {
      var response = await HttpUtils.post(Api.myUpInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的邀请信息
  ///
  static Future<ApiResponse<dynamic>> getInvite() async {
    try {
      var response = await HttpUtils.post(Api.getInvite, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 修改交易密码
  /// [type] 类型:phone=手机号,email=邮箱
  /// [countryCode] 国家区号
  /// [userLogin] 账号
  /// [code] 验证码
  /// [password] 密码
  ///
  static Future<ApiResponse<dynamic>> setTradepass(
      {String type = '',
      String countryCode = '',
      String userLogin = '',
      String code = '',
      String password = ''}) async {
    try {
      var response = await HttpUtils.post(Api.setTradepass, data: {
        'type': type,
        'country_code': countryCode,
        'user_login': userLogin,
        'code': code,
        'password': password,
        'source': AppUtils.getSource(),
        'mobileid': await AppUtils.getDeviceId()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 修改用户信息
  /// [fields] 修改信息，json字符串
  ///
  static Future<ApiResponse<dynamic>> updateFields(String fields) async {
    try {
      var response = await HttpUtils.post(Api.updateFields, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'fields': fields
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户的粉丝列表
  /// [page] 页数
  /// [touid] 对方ID
  ///
  static Future<ApiResponse<dynamic>> getFansList(
      {int? page, int? touid}) async {
    try {
      var response = await HttpUtils.post(Api.getFansList,
          data: {'uid': CustomSpUtil.getUid(), 'p': page, 'touid': touid});
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户的关注列表
  /// [page] 页数
  /// [touid] 对方ID
  /// [key] 搜索关键词
  ///
  static Future<ApiResponse<dynamic>> getFollowsList(
      {int? page, int? touid, String? key}) async {
    try {
      var response = await HttpUtils.post(Api.getFollowsList, data: {
        'uid': CustomSpUtil.getUid(),
        'p': page,
        'touid': touid,
        'key': key
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 关注/取消关注
  /// [touid] 对方ID
  ///
  static Future<ApiResponse<dynamic>> setAttent({
    int? touid,
  }) async {
    try {
      var response = await HttpUtils.post(Api.setAttent, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'touid': touid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 登录奖励(签到)
  ///
  static Future<ApiResponse<dynamic>> userGetBonus() async {
    try {
      var response = await HttpUtils.post(Api.userGetBonus, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于取我的分红信息
  ///
  static Future<ApiResponse<dynamic>> getDivideInfo() async {
    try {
      var response = await HttpUtils.post(Api.getDivideInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用于取我的每日分红
  ///
  static Future<ApiResponse<dynamic>> getDivideLine() async {
    try {
      var response = await HttpUtils.post(Api.getDivideLine, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取微时长列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> getWatchtimeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.getWatchtimeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 个人主页
  /// [touid] 对方ID
  ///
  static Future<ApiResponse<dynamic>> getUserHome({
    int? touid,
  }) async {
    try {
      var response = await HttpUtils.post(Api.getUserHome, data: {
        'uid': CustomSpUtil.getUid(),
        'touid': touid,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 首页搜索会员
  /// [page] 页数
  /// [key] 关键词
  ///
  static Future<ApiResponse<dynamic>> homeSearch(
      {int? page, String? key}) async {
    try {
      var response = await HttpUtils.post(Api.homeSearch, data: {
        'uid': CustomSpUtil.getUid(),
        'p': page,
        'key': key,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取今日观看时长
  ///
  static Future<ApiResponse<dynamic>> getTodayWatchtime() async {
    try {
      var response = await HttpUtils.post(Api.getTodayWatchtime, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 纸飞机和谷歌第三方登录
  /// [type] 第三方标识,google,telegram
  /// [accessToken] 三方接口调用凭证或json字符串
  ///
  static Future<ApiResponse<dynamic>> userLoginByThird2({
    required String utmSource,
    required String accessToken,
    String? niceName,
    String? avatar,
    String? fcmToken,
    String? agentCode,
  }) async {
    try {
      Map<String, dynamic> data = {
        'type': 'other',
        'utm_source': utmSource,
        'access_token': accessToken,
        "agentcode": agentCode,
        'source': AppUtils.getSource(),
        'mobileid': await AppUtils.getDeviceId(),
        'nicename': niceName,
        'avatar': avatar,
      };
      logger.i(data);
      var response = await HttpUtils.post(Api.userLoginByThird2, data: data);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 通过邀请码建立上下级关系
  /// [agentcode] 邀请码
  ///
  static Future<ApiResponse<dynamic>> agentSetAgent(String agentcode) async {
    try {
      var response = await HttpUtils.post(Api.agentSetAgent, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'agentcode': agentcode
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的下级信息
  ///
  static Future<ApiResponse<dynamic>> userMyDownInfo() async {
    try {
      var response = await HttpUtils.post(Api.userMyDownInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的下级列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> userMyDownList(
      {int page = 1, String keyword = '', String type = '0'}) async {
    try {
      var response = await HttpUtils.post(Api.userMyDownList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'keyword': keyword,
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取高级任务的信息
  ///
  static Future<ApiResponse<dynamic>> userSeeTopTasks() async {
    try {
      var response = await HttpUtils.post(Api.userSeeTopTasks, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 领取高级任务奖励
  /// [type] 类型,1=铭文观看视频奖励
  ///
  static Future<ApiResponse<dynamic>> receiveTopTaskReward(
      {String type = '1'}) async {
    try {
      var response = await HttpUtils.post(Api.receiveTopTaskReward, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取普通活跃度
  ///
  static Future<ApiResponse<dynamic>> getUserActivityList(
      {String? starttime, String? endtime, int page = 1}) async {
    try {
      var response = await HttpUtils.post(Api.getUserActivityList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'endtime': endtime,
        'starttime': starttime,
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取加成活跃度
  ///
  static Future<ApiResponse<dynamic>> getUserAddActivityList(
      {String? starttime, String? endtime, int page = 1, int? status}) async {
    try {
      var response = await HttpUtils.post(Api.getUserAddActivityList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'endtime': endtime,
        'starttime': starttime,
        'status': status,
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取用户秘豆信息
  ///
  static Future<ApiResponse<dynamic>> contractGetUserInfo() async {
    try {
      var response = await HttpUtils.post(Api.contractGetUserInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取个人中心赚了多少
  ///
  static Future<ApiResponse<dynamic>> getHowMoney() async {
    try {
      var response = await HttpUtils.post(Api.getHowMoney);
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }
}
