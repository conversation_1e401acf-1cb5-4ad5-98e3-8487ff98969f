# Generated by pub
# See https://dart.dev/tools/pub/glossary#lockfile
packages:
  _flutterfire_internals:
    dependency: transitive
    description:
      name: _flutterfire_internals
      sha256: "4eec93681221723a686ad580c2e7d960e1017cf1a4e0a263c2573c2c6b0bf5cd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.25"
  adjust_sdk:
    dependency: "direct main"
    description:
      name: adjust_sdk
      sha256: "1e1da3bd53afe1b5cde876f2aab2d572acd552d749f0309acc14a8b1e08fe8c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.4.1"
  archive:
    dependency: transitive
    description:
      name: archive
      sha256: cb6a278ef2dbb298455e1a713bda08524a175630ec643a242c399c932a0a1f7d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.6.1"
  args:
    dependency: transitive
    description:
      name: args
      sha256: "7cf60b9f0cc88203c5a190b4cd62a99feea42759a7fa695010eb5de1c0b2252a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.5.0"
  async:
    dependency: transitive
    description:
      name: async
      sha256: "947bfcf187f74dbc5e146c9eb9c0f10c9f8b30743e341481c1e2ed3ecc18c20c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.11.0"
  audio_session:
    dependency: transitive
    description:
      name: audio_session
      sha256: "343e83bc7809fbda2591a49e525d6b63213ade10c76f15813be9aed6657b3261"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.21"
  barcode_scan2:
    dependency: "direct main"
    description:
      name: barcode_scan2
      sha256: "0b0625d27841a21e36e896195d86b2aada335e3c486f63647cce701495718e16"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.4"
  bindings_compatible:
    dependency: transitive
    description:
      name: bindings_compatible
      sha256: "5dd5189f7512aff8ec180a8a11bd59230aa34a2d743e65e427192b7292a78d87"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  boolean_selector:
    dependency: transitive
    description:
      name: boolean_selector
      sha256: "6cfb5af12253eaf2b368f07bacc5a80d1301a071c73360d746b7f2e32d762c66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  bruno:
    dependency: "direct main"
    description:
      name: bruno
      sha256: "9e55ea79690c0d745d308b921db422b38e7b04a5cfb94ef365fa0b34db3d88e5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.3"
  cached_network_image:
    dependency: "direct main"
    description:
      name: cached_network_image
      sha256: f98972704692ba679db144261172a8e20feb145636c617af0eb4022132a6797f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.0"
  cached_network_image_platform_interface:
    dependency: transitive
    description:
      name: cached_network_image_platform_interface
      sha256: "56aa42a7a01e3c9db8456d9f3f999931f1e05535b5a424271e9a38cabf066613"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  cached_network_image_web:
    dependency: transitive
    description:
      name: cached_network_image_web
      sha256: "759b9a9f8f6ccbb66c185df805fac107f05730b1dab9c64626d1008cca532257"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  camera:
    dependency: transitive
    description:
      name: camera
      sha256: "9499cbc2e51d8eb0beadc158b288380037618ce4e30c9acbc4fae1ac3ecb5797"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.10.5+9"
  camera_android:
    dependency: transitive
    description:
      name: camera_android
      sha256: b350ac087f111467e705b2b76cc1322f7f5bdc122aa83b4b243b0872f390d229
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.10.9+2"
  camera_avfoundation:
    dependency: transitive
    description:
      name: camera_avfoundation
      sha256: "608b56b0880722f703871329c4d7d4c2f379c8e2936940851df7fc041abc6f51"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.13+10"
  camera_platform_interface:
    dependency: transitive
    description:
      name: camera_platform_interface
      sha256: b3ede1f171532e0d83111fe0980b46d17f1aa9788a07a2fbed07366bbdbb9061
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.8.0"
  camera_web:
    dependency: transitive
    description:
      name: camera_web
      sha256: b9235ec0a2ce949daec546f1f3d86f05c3921ed31c7d9ab6b7c03214d152fc2d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.4"
  carousel_slider:
    dependency: "direct main"
    description:
      name: carousel_slider
      sha256: bcc61735345c9ab5cb81073896579e735f81e35fd588907a393143ea986be8ff
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.1.1"
  characters:
    dependency: transitive
    description:
      name: characters
      sha256: "04a925763edad70e8443c99234dc3328f442e811f1d8fd1a72f1c8ad0f69a605"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.0"
  chewie:
    dependency: "direct main"
    description:
      name: chewie
      sha256: "8bc4ac4cf3f316e50a25958c0f5eb9bb12cf7e8308bb1d74a43b230da2cfc144"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.7.5"
  clock:
    dependency: transitive
    description:
      name: clock
      sha256: cb6d7f03e1de671e34607e909a7213e31d7752be4fb66a86d29fe1eb14bfb5cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  collection:
    dependency: transitive
    description:
      name: collection
      sha256: ee67cb0715911d28db6bf4af1026078bd6f0128b07a5f66fb2ed94ec6783c09a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.18.0"
  common_utils:
    dependency: transitive
    description:
      name: common_utils
      sha256: c26884339b13ff99b0739e56f4b02090c84054ed9dd3a045435cd24e7b99c2c1
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  connectivity_plus:
    dependency: "direct main"
    description:
      name: connectivity_plus
      sha256: b74247fad72c171381dbe700ca17da24deac637ab6d43c343b42867acb95c991
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.6"
  connectivity_plus_platform_interface:
    dependency: transitive
    description:
      name: connectivity_plus_platform_interface
      sha256: cf1d1c28f4416f8c654d7dc3cd638ec586076255d407cef3ddbdaf178272a71a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.4"
  convert:
    dependency: transitive
    description:
      name: convert
      sha256: "0f08b14755d163f6e2134cb58222dd25ea2a2ee8a195e53983d57c075324d592"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  cross_file:
    dependency: transitive
    description:
      name: cross_file
      sha256: fedaadfa3a6996f75211d835aaeb8fede285dae94262485698afd832371b9a5e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.3+8"
  crypto:
    dependency: "direct main"
    description:
      name: crypto
      sha256: ff625774173754681d66daaf4a448684fb04b78f902da9cb3d308c19cc5e8bab
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.3"
  csslib:
    dependency: transitive
    description:
      name: csslib
      sha256: "09bad715f418841f976c77db72d5398dc1253c21fb9c0c7f0b0b985860b2d58e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.2"
  cupertino_icons:
    dependency: "direct main"
    description:
      name: cupertino_icons
      sha256: ba631d1c7f7bef6b729a622b7b752645a2d076dba9976925b8f25725a30e1ee6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.8"
  dbus:
    dependency: transitive
    description:
      name: dbus
      sha256: "79e0c23480ff85dc68de79e2cd6334add97e48f7f4865d17686dd6ea81a47e8c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.7.11"
  decimal:
    dependency: transitive
    description:
      name: decimal
      sha256: "4140a688f9e443e2f4de3a1162387bf25e1ac6d51e24c9da263f245210f41440"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.2"
  device_info_plus:
    dependency: "direct main"
    description:
      name: device_info_plus
      sha256: "77f757b789ff68e4eaf9c56d1752309bd9f7ad557cb105b938a7f8eb89e59110"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.1.2"
  device_info_plus_platform_interface:
    dependency: transitive
    description:
      name: device_info_plus_platform_interface
      sha256: "0b04e02b30791224b31969eb1b50d723498f402971bff3630bca2ba839bd1ed2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.2"
  dio:
    dependency: "direct main"
    description:
      name: dio
      sha256: "7d328c4d898a61efc3cd93655a0955858e29a0aa647f0f9e02d59b3bb275e2e8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.6"
  equatable:
    dependency: transitive
    description:
      name: equatable
      sha256: "567c64b3cb4cf82397aac55f4f0cbd3ca20d77c6c03bedbc4ceaddc08904aef7"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.7"
  event_bus_plus:
    dependency: "direct main"
    description:
      name: event_bus_plus
      sha256: a8381b29384d04ccd742e53668bc8b3ea99ee80496b26adf23fcceba8cdeb673
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.2"
  extended_image:
    dependency: transitive
    description:
      name: extended_image
      sha256: "69d4299043334ecece679996e47d0b0891cd8c29d8da0034868443506f1d9a78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.3.1"
  extended_image_library:
    dependency: transitive
    description:
      name: extended_image_library
      sha256: "9b55fc5ebc65fad984de66b8f177a1bef2a84d79203c9c213f75ff83c2c29edd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.1"
  extended_nested_scroll_view:
    dependency: "direct main"
    description:
      name: extended_nested_scroll_view
      sha256: "835580d40c2c62b448bd14adecd316acba469ba61f1510ef559d17668a85e777"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.1"
  facebook_auth_desktop:
    dependency: transitive
    description:
      name: facebook_auth_desktop
      sha256: "0e4f147a57de8fdb8eaaee4836e6b9859482921143af0c350ffbf2a9bbd531a0"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.0"
  fake_async:
    dependency: transitive
    description:
      name: fake_async
      sha256: "511392330127add0b769b75a987850d136345d9227c6b94c96a04cf4a391bf78"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.1"
  ffi:
    dependency: transitive
    description:
      name: ffi
      sha256: "7bf0adc28a23d395f19f3f1eb21dd7cfd1dd9f8e1c50051c069122e6853bc878"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  file:
    dependency: transitive
    description:
      name: file
      sha256: a3b4f84adafef897088c160faf7dfffb7696046cb13ae90b508c2cbc95d3b8d4
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.1"
  firebase_analytics:
    dependency: "direct main"
    description:
      name: firebase_analytics
      sha256: b13cbf1ee78744ca5e6b762e9218db3bd3967a0edfed75f58339907892a2ccb9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "10.8.9"
  firebase_analytics_platform_interface:
    dependency: transitive
    description:
      name: firebase_analytics_platform_interface
      sha256: "416b33d62033db5ecd2df719fcb657ad04e9995fa0fc392ffdab4ca0e76cb679"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.9.9"
  firebase_analytics_web:
    dependency: transitive
    description:
      name: firebase_analytics_web
      sha256: "9dca9d8d468172444ef18cabb73fe99f7aae24733bfad67115bd36bffd2d65c1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.5+21"
  firebase_core:
    dependency: "direct main"
    description:
      name: firebase_core
      sha256: "53316975310c8af75a96e365f9fccb67d1c544ef0acdbf0d88bbe30eedd1c4f9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.27.0"
  firebase_core_platform_interface:
    dependency: transitive
    description:
      name: firebase_core_platform_interface
      sha256: "8bcfad6d7033f5ea951d15b867622a824b13812178bfec0c779b9d81de011bbb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.4.2"
  firebase_core_web:
    dependency: transitive
    description:
      name: firebase_core_web
      sha256: c8e1d59385eee98de63c92f961d2a7062c5d9a65e7f45bdc7f1b0b205aab2492
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.11.5"
  firebase_messaging:
    dependency: transitive
    description:
      name: firebase_messaging
      sha256: "980259425fa5e2afc03e533f33723335731d21a56fd255611083bceebf4373a8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "14.7.10"
  firebase_messaging_platform_interface:
    dependency: transitive
    description:
      name: firebase_messaging_platform_interface
      sha256: f7a9d74ff7fc588a924f6b2eaeaa148b0db521b13a9db55f6ad45864fa98c06e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.27"
  firebase_messaging_web:
    dependency: transitive
    description:
      name: firebase_messaging_web
      sha256: "90dc7ed885e90a24bb0e56d661d4d2b5f84429697fd2cbb9e5890a0ca370e6f4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.5.18"
  fixnum:
    dependency: transitive
    description:
      name: fixnum
      sha256: b6dc7065e46c974bc7c5f143080a6764ec7a4be6da1285ececdc37be96de53be
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  fl_chart:
    dependency: "direct main"
    description:
      name: fl_chart
      sha256: "74959b99b92b9eebeed1a4049426fd67c4abc3c5a0f4d12e2877097d6a11ae08"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.69.2"
  flustars_flutter3:
    dependency: "direct main"
    description:
      name: flustars_flutter3
      sha256: d4f412eeb97e648dfe5cf888aa9066f6c1b1f133ff72dca264c0d23a906e3dab
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  flutter:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_cache_manager:
    dependency: transitive
    description:
      name: flutter_cache_manager
      sha256: "8207f27539deb83732fdda03e259349046a39a4c767269285f449ade355d54ba"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.3.1"
  flutter_easyloading:
    dependency: "direct main"
    description:
      name: flutter_easyloading
      sha256: ba21a3c883544e582f9cc455a4a0907556714e1e9cf0eababfcb600da191d17c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.5"
  flutter_easyrefresh:
    dependency: transitive
    description:
      name: flutter_easyrefresh
      sha256: "5d161ee5dcac34da9065116568147d742dd25fb9bff3b10024d9054b195087ad"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  flutter_facebook_auth:
    dependency: "direct main"
    description:
      name: flutter_facebook_auth
      sha256: "48aab06f3b1d3494a33b0c10c11ecbc226fdcf0bf37b478e91d9de57cce840f5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.1"
  flutter_facebook_auth_platform_interface:
    dependency: transitive
    description:
      name: flutter_facebook_auth_platform_interface
      sha256: dc9d621dd45c4f0b341173a16e94f4b77155fa9c0f4326743f1251f2f445ba38
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.0"
  flutter_facebook_auth_web:
    dependency: transitive
    description:
      name: flutter_facebook_auth_web
      sha256: "947d93fc5a7cc5db1ce0274505254bb3b619cdd98176954f125f742964696804"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.0"
  flutter_fcm:
    dependency: "direct main"
    description:
      name: flutter_fcm
      sha256: e5c22d57292b9bf04c34b97c4d9e4fa71f0052c9b4fb34913a17c1cafeaab3ba
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  flutter_hls_parser:
    dependency: "direct main"
    description:
      name: flutter_hls_parser
      sha256: f4b7df4f927623aea5c72006232693e07fdd11438f600c08670d6a23c1aa85c8
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  flutter_inappwebview:
    dependency: "direct main"
    description:
      name: flutter_inappwebview
      sha256: f73505c792cf083d5566e1a94002311be497d984b5607f25be36d685cf6361cf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.7.2+3"
  flutter_lints:
    dependency: "direct dev"
    description:
      name: flutter_lints
      sha256: a25a15ebbdfc33ab1cd26c63a6ee519df92338a9c10f122adda92938253bef04
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  flutter_local_notifications:
    dependency: transitive
    description:
      name: flutter_local_notifications
      sha256: "55b9b229307a10974b26296ff29f2e132256ba4bd74266939118eaefa941cb00"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "16.3.3"
  flutter_local_notifications_linux:
    dependency: transitive
    description:
      name: flutter_local_notifications_linux
      sha256: c49bd06165cad9beeb79090b18cd1eb0296f4bf4b23b84426e37dd7c027fc3af
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.1"
  flutter_local_notifications_platform_interface:
    dependency: transitive
    description:
      name: flutter_local_notifications_platform_interface
      sha256: "85f8d07fe708c1bdcf45037f2c0109753b26ae077e9d9e899d55971711a4ea66"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.2.0"
  flutter_localizations:
    dependency: "direct main"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_picker:
    dependency: "direct main"
    description:
      name: flutter_picker
      sha256: "2f94c6eefba8697b07e3cd008b75f06b4ba7053cb26d23ae0fcd5932b7dc75af"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  flutter_plugin_android_lifecycle:
    dependency: transitive
    description:
      name: flutter_plugin_android_lifecycle
      sha256: "8cf40eebf5dec866a6d1956ad7b4f7016e6c0cc69847ab946833b7d43743809f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.19"
  flutter_secure_storage:
    dependency: transitive
    description:
      name: flutter_secure_storage
      sha256: "9cad52d75ebc511adfae3d447d5d13da15a55a92c9410e50f67335b6d21d16ea"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.2.4"
  flutter_secure_storage_linux:
    dependency: transitive
    description:
      name: flutter_secure_storage_linux
      sha256: be76c1d24a97d0b98f8b54bce6b481a380a6590df992d0098f868ad54dc8f688
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.3"
  flutter_secure_storage_macos:
    dependency: transitive
    description:
      name: flutter_secure_storage_macos
      sha256: "6c0a2795a2d1de26ae202a0d78527d163f4acbb11cde4c75c670f3a0fc064247"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.3"
  flutter_secure_storage_platform_interface:
    dependency: transitive
    description:
      name: flutter_secure_storage_platform_interface
      sha256: cf91ad32ce5adef6fba4d736a542baca9daf3beac4db2d04be350b87f69ac4a8
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.2"
  flutter_secure_storage_web:
    dependency: transitive
    description:
      name: flutter_secure_storage_web
      sha256: f4ebff989b4f07b2656fb16b47852c0aab9fed9b4ec1c70103368337bc1886a9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  flutter_secure_storage_windows:
    dependency: transitive
    description:
      name: flutter_secure_storage_windows
      sha256: b20b07cb5ed4ed74fc567b78a72936203f587eba460af1df11281c9326cd3709
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
  flutter_slidable:
    dependency: "direct main"
    description:
      name: flutter_slidable
      sha256: a857de7ea701f276fd6a6c4c67ae885b60729a3449e42766bb0e655171042801
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
  flutter_spinkit:
    dependency: transitive
    description:
      name: flutter_spinkit
      sha256: d2696eed13732831414595b98863260e33e8882fc069ee80ec35d4ac9ddb0472
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.1"
  flutter_svg:
    dependency: "direct main"
    description:
      name: flutter_svg
      sha256: "7b4ca6cf3304575fe9c8ec64813c8d02ee41d2afe60bcfe0678bcb5375d596a2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.10+1"
  flutter_test:
    dependency: "direct dev"
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_web_plugins:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.0"
  flutter_widget_from_html:
    dependency: "direct main"
    description:
      name: flutter_widget_from_html
      sha256: f3967a5b42896662efdd420b5adaf8a7d3692b0f44462a07c80e3b4c173b1a02
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.3"
  flutter_widget_from_html_core:
    dependency: transitive
    description:
      name: flutter_widget_from_html_core
      sha256: b1048fd119a14762e2361bd057da608148a895477846d6149109b2151d2f7abf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.2"
  fwfh_cached_network_image:
    dependency: transitive
    description:
      name: fwfh_cached_network_image
      sha256: "8e44226801bfba27930673953afce8af44da7e92573be93f60385d9865a089dd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.14.3"
  fwfh_chewie:
    dependency: transitive
    description:
      name: fwfh_chewie
      sha256: "37bde9cedfb6dc5546176f7f0c56af1e814966cb33ec58f16c9565ed93ccb704"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.14.8"
  fwfh_just_audio:
    dependency: transitive
    description:
      name: fwfh_just_audio
      sha256: "38dc2c55803bd3cef33042c473e0c40b891ad4548078424641a32032f6a1245f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.2"
  fwfh_svg:
    dependency: transitive
    description:
      name: fwfh_svg
      sha256: "550b1014d12b5528d8bdb6e3b44b58721f3fb1f65d7a852d1623a817008bdfc4"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.8.3"
  fwfh_url_launcher:
    dependency: transitive
    description:
      name: fwfh_url_launcher
      sha256: b9f5d55a5ae2c2c07243ba33f7ba49ac9544bdb2f4c16d8139df9ccbebe3449c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.1"
  fwfh_webview:
    dependency: transitive
    description:
      name: fwfh_webview
      sha256: "06595c7ca945c8d8522864a764e21abbcf50096852f8d256e45c0fa101b6fbc6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.5"
  get:
    dependency: "direct main"
    description:
      name: get
      sha256: e4e7335ede17452b391ed3b2ede016545706c01a02292a6c97619705e7d2a85e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.6.6"
  google_api_availability:
    dependency: "direct main"
    description:
      name: google_api_availability
      sha256: "1642876fa87515fd5e4074458f22d6ba4518919c5abce16baaa2878c3c555678"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.0"
  google_identity_services_web:
    dependency: transitive
    description:
      name: google_identity_services_web
      sha256: "0c56c2c5d60d6dfaf9725f5ad4699f04749fb196ee5a70487a46ef184837ccf6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.0+2"
  google_sign_in:
    dependency: "direct main"
    description:
      name: google_sign_in
      sha256: "8f8b94880f2753ccb796744259da529674e49b9af2e372abf6978c590c0ebfef"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.6"
  google_sign_in_android:
    dependency: transitive
    description:
      name: google_sign_in_android
      sha256: "7647893c65e6720973f0e579051c8f84b877b486614d9f70a404259c41a4632e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.23"
  google_sign_in_ios:
    dependency: transitive
    description:
      name: google_sign_in_ios
      sha256: f3336d9e44d4d28063ac90271f6db5caf99f0480cb07281330e7a432edb95226
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.7.3"
  google_sign_in_platform_interface:
    dependency: transitive
    description:
      name: google_sign_in_platform_interface
      sha256: "1f6e5787d7a120cc0359ddf315c92309069171306242e181c09472d1b00a2971"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.5"
  google_sign_in_web:
    dependency: transitive
    description:
      name: google_sign_in_web
      sha256: a278ea2d01013faf341cbb093da880d0f2a552bbd1cb6ee90b5bebac9ba69d77
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.3+2"
  html:
    dependency: transitive
    description:
      name: html
      sha256: "6d1264f2dffa1b1101c25a91dff0dc2daee4c18e87cd8538729773c073dbf602"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.15.6"
  http:
    dependency: "direct overridden"
    description:
      name: http
      sha256: "5895291c13fa8a3bd82e76d5627f69e0d85ca6a30dcac95c4ea19a5d555879c2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.13.6"
  http_client_helper:
    dependency: transitive
    description:
      name: http_client_helper
      sha256: "8a9127650734da86b5c73760de2b404494c968a3fd55602045ffec789dac3cb1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  http_parser:
    dependency: transitive
    description:
      name: http_parser
      sha256: "2aa08ce0341cc9b354a498388e30986515406668dbcc4f7c950c3e715496693b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  image_gallery_saver:
    dependency: "direct main"
    description:
      name: image_gallery_saver
      sha256: "0aba74216a4d9b0561510cb968015d56b701ba1bd94aace26aacdd8ae5761816"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  intl:
    dependency: "direct overridden"
    description:
      name: intl
      sha256: "3bc132a9dbce73a7e4a21a17d06e1878839ffbf975568bc875c60537824b0c4d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.18.1"
  js:
    dependency: transitive
    description:
      name: js
      sha256: f2c445dce49627136094980615a031419f7f3eb393237e4ecd97ac15dea343f3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.7"
  just_audio:
    dependency: "direct main"
    description:
      name: just_audio
      sha256: a49e7120b95600bd357f37a2bb04cd1e88252f7cdea8f3368803779b925b1049
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.42"
  just_audio_platform_interface:
    dependency: transitive
    description:
      name: just_audio_platform_interface
      sha256: "4cd94536af0219fa306205a58e78d67e02b0555283c1c094ee41e402a14a5c4a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.0"
  just_audio_web:
    dependency: transitive
    description:
      name: just_audio_web
      sha256: "0edb481ad4aa1ff38f8c40f1a3576013c3420bf6669b686fe661627d49bc606c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.11"
  lints:
    dependency: transitive
    description:
      name: lints
      sha256: "0a217c6c989d21039f1498c3ed9f3ed71b354e69873f13a8dfc3c9fe76f1b452"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  logger:
    dependency: transitive
    description:
      name: logger
      sha256: "2621da01aabaf223f8f961e751f2c943dbb374dc3559b982f200ccedadaa6999"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.6.0"
  logging:
    dependency: transitive
    description:
      name: logging
      sha256: "623a88c9594aa774443aa3eb2d41807a48486b5613e67599fb4c41c0ad47c340"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  lottie:
    dependency: "direct main"
    description:
      name: lottie
      sha256: "46def1e76c4fbfd4643e823980112cfe94a2ba1d9152fe54701c0bf30be4f4cd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  lpinyin:
    dependency: transitive
    description:
      name: lpinyin
      sha256: "0bb843363f1f65170efd09fbdfc760c7ec34fc6354f9fcb2f89e74866a0d814a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  matcher:
    dependency: transitive
    description:
      name: matcher
      sha256: "1803e76e6653768d64ed8ff2e1e67bea3ad4b923eb5c56a295c3e634bad5960e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.12.16"
  material_color_utilities:
    dependency: transitive
    description:
      name: material_color_utilities
      sha256: "9528f2f296073ff54cb9fee677df673ace1218163c3bc7628093e7eed5203d41"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.0"
  meta:
    dependency: transitive
    description:
      name: meta
      sha256: a6e590c838b18133bb482a2745ad77c5bb7715fb0451209e1a7567d416678b8e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.0"
  mime:
    dependency: transitive
    description:
      name: mime
      sha256: "801fd0b26f14a4a58ccb09d5892c3fbdeff209594300a542492cf13fba9d247a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.6"
  mime_type:
    dependency: transitive
    description:
      name: mime_type
      sha256: d652b613e84dac1af28030a9fba82c0999be05b98163f9e18a0849c6e63838bb
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  nested:
    dependency: transitive
    description:
      name: nested
      sha256: "03bac4c528c64c95c722ec99280375a6f2fc708eec17c7b3f07253b626cd2a20"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  nm:
    dependency: transitive
    description:
      name: nm
      sha256: "2c9aae4127bdc8993206464fcc063611e0e36e72018696cd9631023a31b24254"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.5.0"
  octo_image:
    dependency: transitive
    description:
      name: octo_image
      sha256: "34faa6639a78c7e3cbe79be6f9f96535867e879748ade7d17c9b1ae7536293bd"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  openinstall_flutter_global:
    dependency: "direct main"
    description:
      name: openinstall_flutter_global
      sha256: "1018a261ba2e34e0696751c170c0125f3527724d80846f60976e145211340b38"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.0"
  package_info_plus:
    dependency: "direct main"
    description:
      name: package_info_plus
      sha256: "7e76fad405b3e4016cd39d08f455a4eb5199723cf594cd1b8916d47140d93017"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.0"
  package_info_plus_platform_interface:
    dependency: transitive
    description:
      name: package_info_plus_platform_interface
      sha256: "9bc8ba46813a4cc42c66ab781470711781940780fd8beddd0c3da62506d3a6c6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.1"
  path:
    dependency: transitive
    description:
      name: path
      sha256: "8829d8a55c13fc0e37127c29fedf290c102f4e40ae94ada574091fe0ff96c917"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.8.3"
  path_drawing:
    dependency: "direct overridden"
    description:
      name: path_drawing
      sha256: bbb1934c0cbb03091af082a6389ca2080345291ef07a5fa6d6e078ba8682f977
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  path_parsing:
    dependency: transitive
    description:
      name: path_parsing
      sha256: e3e67b1629e6f7e8100b367d3db6ba6af4b1f0bb80f64db18ef1fbabd2fa9ccf
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.1"
  path_provider:
    dependency: "direct main"
    description:
      name: path_provider
      sha256: a1aa8aaa2542a6bc57e381f132af822420216c80d4781f7aa085ca3229208aaa
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  path_provider_android:
    dependency: transitive
    description:
      name: path_provider_android
      sha256: a248d8146ee5983446bf03ed5ea8f6533129a12b11f12057ad1b4a67a2b3b41d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.4"
  path_provider_foundation:
    dependency: transitive
    description:
      name: path_provider_foundation
      sha256: "5a7999be66e000916500be4f15a3633ebceb8302719b47b9cc49ce924125350f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  path_provider_linux:
    dependency: transitive
    description:
      name: path_provider_linux
      sha256: f7a1fe3a634fe7734c8d3f2766ad746ae2a2884abe22e241a8b301bf5cac3279
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.1"
  path_provider_platform_interface:
    dependency: transitive
    description:
      name: path_provider_platform_interface
      sha256: "88f5779f72ba699763fa3a3b06aa4bf6de76c8e5de842cf6f29e2e06476c2334"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  path_provider_windows:
    dependency: transitive
    description:
      name: path_provider_windows
      sha256: bd6f00dbd873bfb70d0761682da2b3a2c2fccc2b9e84c495821639601d81afe7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.0"
  permission_handler:
    dependency: "direct main"
    description:
      name: permission_handler
      sha256: "18bf33f7fefbd812f37e72091a15575e72d5318854877e0e4035a24ac1113ecb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "11.3.1"
  permission_handler_android:
    dependency: transitive
    description:
      name: permission_handler_android
      sha256: "71bbecfee799e65aff7c744761a57e817e73b738fedf62ab7afd5593da21f9f1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "12.0.13"
  permission_handler_apple:
    dependency: transitive
    description:
      name: permission_handler_apple
      sha256: f000131e755c54cf4d84a5d8bd6e4149e262cc31c5a8b1d698de1ac85fa41023
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "9.4.7"
  permission_handler_html:
    dependency: transitive
    description:
      name: permission_handler_html
      sha256: "54bf176b90f6eddd4ece307e2c06cf977fb3973719c35a93b85cc7093eb6070d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.1.1"
  permission_handler_platform_interface:
    dependency: transitive
    description:
      name: permission_handler_platform_interface
      sha256: e9c8eadee926c4532d0305dff94b85bf961f16759c3af791486613152af4b4f9
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.2.3"
  permission_handler_windows:
    dependency: transitive
    description:
      name: permission_handler_windows
      sha256: "1a790728016f79a41216d88672dbc5df30e686e811ad4e698bfc51f76ad91f1e"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.1"
  petitparser:
    dependency: transitive
    description:
      name: petitparser
      sha256: c15605cd28af66339f8eb6fbe0e541bfe2d1b72d5825efc6598f3e0a31b9ad27
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.0.2"
  photo_manager:
    dependency: transitive
    description:
      name: photo_manager
      sha256: a0d9a7a9bc35eda02d33766412bde6d883a8b0acb86bbe37dac5f691a0894e8a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.7.1"
  photo_manager_image_provider:
    dependency: transitive
    description:
      name: photo_manager_image_provider
      sha256: b6015b67b32f345f57cf32c126f871bced2501236c405aafaefa885f7c821e4f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  photo_view:
    dependency: transitive
    description:
      name: photo_view
      sha256: "8036802a00bae2a78fc197af8a158e3e2f7b500561ed23b4c458107685e645bb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.14.0"
  platform:
    dependency: transitive
    description:
      name: platform
      sha256: "5d6b1b0036a5f331ebc77c850ebc8506cbc1e9416c27e59b439f917a902a4984"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.6"
  plugin_platform_interface:
    dependency: transitive
    description:
      name: plugin_platform_interface
      sha256: "4820fbfdb9478b1ebae27888254d445073732dae3d6ea81f0b7e06d5dedc3f02"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.8"
  preload_page_view:
    dependency: "direct main"
    description:
      name: preload_page_view
      sha256: "488a10c158c5c2e9ba9d77e5dbc09b1e49e37a20df2301e5ba02992eac802b7a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.2.0"
  pretty_dio_logger:
    dependency: "direct main"
    description:
      name: pretty_dio_logger
      sha256: "948f7eeb36e7aa0760b51c1a8e3331d4b21e36fabd39efca81f585ed93893544"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0-beta-1"
  pretty_qr_code:
    dependency: "direct main"
    description:
      name: pretty_qr_code
      sha256: b078bd5d51956dea4342378af1b092ad962b81bdbb55b10fffce03461da8db74
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.0"
  protobuf:
    dependency: "direct overridden"
    description:
      name: protobuf
      sha256: "01dd9bd0fa02548bf2ceee13545d4a0ec6046459d847b6b061d8a27237108a08"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.0"
  provider:
    dependency: transitive
    description:
      name: provider
      sha256: "4abbd070a04e9ddc287673bf5a030c7ca8b685ff70218720abab8b092f53dd84"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.1.5"
  pull_to_refresh_flutter3:
    dependency: "direct main"
    description:
      name: pull_to_refresh_flutter3
      sha256: "37a88d901cca9a46dbdd46523de8e7b35a3e58634a0e775b1a5904981f69b353"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.2"
  qr:
    dependency: transitive
    description:
      name: qr
      sha256: "64957a3930367bf97cc211a5af99551d630f2f4625e38af10edd6b19131b64b3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.1"
  quiver:
    dependency: transitive
    description:
      name: quiver
      sha256: ea0b925899e64ecdfbf9c7becb60d5b50e706ade44a85b2363be2a22d88117d2
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.2"
  rational:
    dependency: transitive
    description:
      name: rational
      sha256: cb808fb6f1a839e6fc5f7d8cb3b0a10e1db48b3be102de73938c627f0b636336
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  rxdart:
    dependency: transitive
    description:
      name: rxdart
      sha256: "0c7c0cedd93788d996e33041ffecda924cc54389199cde4e6a34b440f50044cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.27.7"
  scrollview_observer:
    dependency: "direct main"
    description:
      name: scrollview_observer
      sha256: "174d4efe7b79459a07662175c4db42c9862dcf78d3978e6e9c2d6c0d8137f4ca"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.26.1"
  sensors_plus:
    dependency: transitive
    description:
      name: sensors_plus
      sha256: "8e7fa79b4940442bb595bfc0ee9da4af5a22a0fe6ebacc74998245ee9496a82d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.0.2"
  sensors_plus_platform_interface:
    dependency: transitive
    description:
      name: sensors_plus_platform_interface
      sha256: bc472d6cfd622acb4f020e726433ee31788b038056691ba433fec80e448a094f
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  share_plus:
    dependency: "direct main"
    description:
      name: share_plus
      sha256: "3ef39599b00059db0990ca2e30fca0a29d8b37aae924d60063f8e0184cf20900"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.2.2"
  share_plus_platform_interface:
    dependency: transitive
    description:
      name: share_plus_platform_interface
      sha256: "251eb156a8b5fa9ce033747d73535bf53911071f8d3b6f4f0b578505ce0d4496"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.4.0"
  shared_preferences:
    dependency: "direct main"
    description:
      name: shared_preferences
      sha256: d3bbe5553a986e83980916ded2f0b435ef2e1893dfaa29d5a7a790d0eca12180
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  shared_preferences_android:
    dependency: transitive
    description:
      name: shared_preferences_android
      sha256: "1ee8bf911094a1b592de7ab29add6f826a7331fb854273d55918693d5364a1f2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  shared_preferences_foundation:
    dependency: transitive
    description:
      name: shared_preferences_foundation
      sha256: "7708d83064f38060c7b39db12aefe449cb8cdc031d6062280087bc4cdb988f5c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.5"
  shared_preferences_linux:
    dependency: transitive
    description:
      name: shared_preferences_linux
      sha256: "2ba0510d3017f91655b7543e9ee46d48619de2a2af38e5c790423f7007c7ccc1"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  shared_preferences_platform_interface:
    dependency: transitive
    description:
      name: shared_preferences_platform_interface
      sha256: "57cbf196c486bc2cf1f02b85784932c6094376284b3ad5779d1b1c6c6a816b80"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.1"
  shared_preferences_web:
    dependency: transitive
    description:
      name: shared_preferences_web
      sha256: "7b15ffb9387ea3e237bb7a66b8a23d2147663d391cafc5c8f37b2e7b4bde5d21"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.2"
  shared_preferences_windows:
    dependency: transitive
    description:
      name: shared_preferences_windows
      sha256: "398084b47b7f92110683cac45c6dc4aae853db47e470e5ddcd52cab7f7196ab2"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.0"
  shimmer:
    dependency: "direct main"
    description:
      name: shimmer
      sha256: "5f88c883a22e9f9f299e5ba0e4f7e6054857224976a5d9f839d4ebdc94a14ac9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  sky_engine:
    dependency: transitive
    description: flutter
    source: sdk
    version: "0.0.99"
  source_span:
    dependency: transitive
    description:
      name: source_span
      sha256: "53e943d4206a5e30df338fd4c6e7a077e02254531b138a15aec3bd143c1a8b3c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.10.0"
  sp_util:
    dependency: transitive
    description:
      name: sp_util
      sha256: "9da43dce5de79c17a787d0626bf01538d63090ca32521200d22a232171c495dc"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.0.3"
  sprintf:
    dependency: transitive
    description:
      name: sprintf
      sha256: "1fc9ffe69d4df602376b52949af107d8f5703b77cda567c4d7d86a0693120f23"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "7.0.0"
  sqflite:
    dependency: transitive
    description:
      name: sqflite
      sha256: a9016f495c927cb90557c909ff26a6d92d9bd54fc42ba92e19d4e79d61e798c6
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  sqflite_common:
    dependency: transitive
    description:
      name: sqflite_common
      sha256: "28d8c66baee4968519fb8bd6cdbedad982d6e53359091f0b74544a9f32ec72d5"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.5.3"
  stack_trace:
    dependency: transitive
    description:
      name: stack_trace
      sha256: "73713990125a6d93122541237550ee3352a2d84baad52d375a4cad2eb9b7ce0b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.11.1"
  stream_channel:
    dependency: transitive
    description:
      name: stream_channel
      sha256: ba2aa5d8cc609d96bbb2899c28934f9e1af5cddbd60a827822ea467161eb54e7
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.2"
  stream_transform:
    dependency: transitive
    description:
      name: stream_transform
      sha256: ad47125e588cfd37a9a7f86c7d6356dde8dfe89d071d293f80ca9e9273a33871
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.1"
  string_scanner:
    dependency: transitive
    description:
      name: string_scanner
      sha256: "556692adab6cfa87322a115640c11f13cb77b3f076ddcc5d6ae3c20242bedcde"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.0"
  svgaplayer_flutter:
    dependency: "direct main"
    description:
      name: svgaplayer_flutter
      sha256: "13ad44aabb454e0094962b260c5333662fbd2879c7f9f8d2595b3c4b79817651"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.0"
  synchronized:
    dependency: transitive
    description:
      name: synchronized
      sha256: "539ef412b170d65ecdafd780f924e5be3f60032a1128df156adad6c5b373d558"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.0+1"
  tencent_cloud_chat_sdk:
    dependency: "direct main"
    description:
      path: "plugins/tencent_cloud_chat_sdk-8.5.6864+2"
      relative: true
    source: path
    version: "8.5.6864+2"
  tencent_cos_plus:
    dependency: "direct main"
    description:
      name: tencent_cos_plus
      sha256: "78dac59a291528427cafa07d94001de695daa43a7497f67a41617da306d3de9d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.2"
  term_glyph:
    dependency: transitive
    description:
      name: term_glyph
      sha256: a29248a84fbb7c79282b40b8c72a1209db169a2e0542bce341da992fe1bc7e84
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.2.1"
  test_api:
    dependency: transitive
    description:
      name: test_api
      sha256: "5c2f730018264d276c20e4f1503fd1308dfbbae39ec8ee63c5236311ac06954b"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.6.1"
  timezone:
    dependency: transitive
    description:
      name: timezone
      sha256: "2236ec079a174ce07434e89fcd3fcda430025eb7692244139a9cf54fdcf1fc7d"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.9.4"
  typed_data:
    dependency: transitive
    description:
      name: typed_data
      sha256: facc8d6582f16042dd49f2463ff1bd6e2c9ef9f3d5da3d9b087e244a7b564b3c
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.3.2"
  url_launcher:
    dependency: transitive
    description:
      name: url_launcher
      sha256: "21b704ce5fa560ea9f3b525b43601c678728ba46725bab9b01187b4831377ed3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.0"
  url_launcher_android:
    dependency: transitive
    description:
      name: url_launcher_android
      sha256: "17cd5e205ea615e2c6ea7a77323a11712dffa0720a8a90540db57a01347f9ad9"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.2"
  url_launcher_ios:
    dependency: "direct overridden"
    description:
      name: url_launcher_ios
      sha256: e43b677296fadce447e987a2f519dcf5f6d1e527dc35d01ffab4fff5b8a7063e
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.3.1"
  url_launcher_linux:
    dependency: transitive
    description:
      name: url_launcher_linux
      sha256: ab360eb661f8879369acac07b6bb3ff09d9471155357da8443fd5d3cf7363811
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.1"
  url_launcher_macos:
    dependency: transitive
    description:
      name: url_launcher_macos
      sha256: "9a1a42d5d2d95400c795b2914c36fdcb525870c752569438e4ebb09a2b5d90de"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.2.0"
  url_launcher_platform_interface:
    dependency: transitive
    description:
      name: url_launcher_platform_interface
      sha256: "552f8a1e663569be95a8190206a38187b531910283c3e982193e4f2733f01029"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.3.2"
  url_launcher_web:
    dependency: transitive
    description:
      name: url_launcher_web
      sha256: fff0932192afeedf63cdd50ecbb1bc825d31aed259f02bb8dba0f3b729a5e88b
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.2.3"
  url_launcher_windows:
    dependency: transitive
    description:
      name: url_launcher_windows
      sha256: "49c10f879746271804767cb45551ec5592cdab00ee105c06dddde1a98f73b185"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.2"
  uuid:
    dependency: transitive
    description:
      name: uuid
      sha256: a5be9ef6618a7ac1e964353ef476418026db906c4facdedaa299b7a2e71690ff
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.1"
  vector_graphics:
    dependency: transitive
    description:
      name: vector_graphics
      sha256: "32c3c684e02f9bc0afb0ae0aa653337a2fe022e8ab064bcd7ffda27a74e288e3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_codec:
    dependency: transitive
    description:
      name: vector_graphics_codec
      sha256: c86987475f162fadff579e7320c7ddda04cd2fdeffbe1129227a85d9ac9e03da
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_graphics_compiler:
    dependency: transitive
    description:
      name: vector_graphics_compiler
      sha256: "12faff3f73b1741a36ca7e31b292ddeb629af819ca9efe9953b70bd63fc8cd81"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.11+1"
  vector_math:
    dependency: transitive
    description:
      name: vector_math
      sha256: "80b3257d1492ce4d091729e3a67a60407d227c27241d6927be0130c98e741803"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.4"
  video_compress:
    dependency: "direct main"
    description:
      name: video_compress
      sha256: "31bc5cdb9a02ba666456e5e1907393c28e6e0e972980d7d8d619a7beda0d4f20"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.1.4"
  video_player:
    dependency: "direct main"
    description:
      name: video_player
      sha256: efa2e24042166906ddf836dd131258d0371d0009cdf0476f6a83fd992a17f5d0
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.8.5"
  video_player_android:
    dependency: transitive
    description:
      name: video_player_android
      sha256: "134e1ad410d67e18a19486ed9512c72dfc6d8ffb284d0e8f2e99e903d1ba8fa3"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.4.14"
  video_player_avfoundation:
    dependency: transitive
    description:
      name: video_player_avfoundation
      sha256: "309e3962795e761be010869bae65c0b0e45b5230c5cee1bec72197ca7db040ed"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.5.6"
  video_player_platform_interface:
    dependency: transitive
    description:
      name: video_player_platform_interface
      sha256: "236454725fafcacf98f0f39af0d7c7ab2ce84762e3b63f2cbb3ef9a7e0550bc6"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.2.2"
  video_player_web:
    dependency: transitive
    description:
      name: video_player_web
      sha256: "34beb3a07d4331a24f7e7b2f75b8e2b103289038e07e65529699a671b6a6e2cb"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.1.3"
  video_player_web_hls:
    dependency: "direct main"
    description:
      name: video_player_web_hls
      sha256: "248970983b0ad09eafbee36deddca9a7032402b26ad1a197e1a43fa84cc0ee45"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.1"
  visibility_detector:
    dependency: "direct main"
    description:
      name: visibility_detector
      sha256: dd5cc11e13494f432d15939c3aa8ae76844c42b723398643ce9addb88a5ed420
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.4.0+2"
  wakelock_plus:
    dependency: transitive
    description:
      name: wakelock_plus
      sha256: f268ca2116db22e57577fb99d52515a24bdc1d570f12ac18bb762361d43b043d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.4"
  wakelock_plus_platform_interface:
    dependency: transitive
    description:
      name: wakelock_plus_platform_interface
      sha256: "40fabed5da06caff0796dc638e1f07ee395fb18801fbff3255a2372db2d80385"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.0"
  web:
    dependency: transitive
    description:
      name: web
      sha256: afe077240a270dcfd2aafe77602b4113645af95d0ad31128cc02bce5ac5d5152
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "0.3.0"
  webview_flutter:
    dependency: transitive
    description:
      name: webview_flutter
      sha256: d81b68e88cc353e546afb93fb38958e3717282c5ac6e5d3be4a4aef9fc3c1413
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.5.0"
  webview_flutter_android:
    dependency: transitive
    description:
      name: webview_flutter_android
      sha256: "0d21cfc3bfdd2e30ab2ebeced66512b91134b39e72e97b43db2d47dda1c4e53a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.16.3"
  webview_flutter_platform_interface:
    dependency: transitive
    description:
      name: webview_flutter_platform_interface
      sha256: d937581d6e558908d7ae3dc1989c4f87b786891ab47bb9df7de548a151779d8d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "2.10.0"
  webview_flutter_wkwebview:
    dependency: transitive
    description:
      name: webview_flutter_wkwebview
      sha256: "4d062ad505390ecef1c4bfb6001cd857a51e00912cc9dfb66edb1886a9ebd80c"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.10.2"
  wechat_assets_picker:
    dependency: "direct main"
    description:
      name: wechat_assets_picker
      sha256: "9934724a45fdb2b12e332d8190c58713e6675c37c630d53608e0f50167215c9f"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "8.9.0-dev.1"
  wechat_camera_picker:
    dependency: "direct main"
    description:
      name: wechat_camera_picker
      sha256: "3ade7162fd510b2f5916f90b0fe6841e3b6f79a5fd70160ed949d2ccdec10eac"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "4.3.2"
  wechat_picker_library:
    dependency: transitive
    description:
      name: wechat_picker_library
      sha256: a42e09cb85b15fc9410f6a69671371cc60aa99c4a1f7967f6593a7f665f6f47a
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.5"
  whatsapp:
    dependency: "direct main"
    description:
      name: whatsapp
      sha256: d8fcb5ec1164a94af2fd2b9deb363328eb1ec996b6b237065228aae0271a6db3
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "3.0.0"
  win32:
    dependency: transitive
    description:
      name: win32
      sha256: "464f5674532865248444b4c3daca12bd9bf2d7c47f759ce2617986e7229494a8"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "5.2.0"
  win32_registry:
    dependency: transitive
    description:
      name: win32_registry
      sha256: "41fd8a189940d8696b1b810efb9abcf60827b6cbfab90b0c43e8439e3a39d85a"
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.1.2"
  xdg_directories:
    dependency: transitive
    description:
      name: xdg_directories
      sha256: faea9dee56b520b55a566385b84f2e8de55e7496104adada9962e0bd11bcff1d
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "1.0.4"
  xml:
    dependency: transitive
    description:
      name: xml
      sha256: b015a8ad1c488f66851d762d3090a21c600e479dc75e68328c52774040cf9226
      url: "https://pub.flutter-io.cn"
    source: hosted
    version: "6.5.0"
sdks:
  dart: ">=3.2.3 <4.0.0"
  flutter: ">=3.16.0"
