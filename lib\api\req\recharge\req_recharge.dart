import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:flutter_starry_sky_box/utils/str_utils.dart';

class ReqRecharge {
  ///
  /// 获取充值列表
  ///
  static Future<ApiResponse<dynamic>> getRuleList(String langType) async {
    try {
      var response = await HttpUtils.post(Api.getRuleList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'lang_type': langType
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取第三方可用充值列表
  /// [type] 0-全部 7-第三方支付 8-区块链
  ///
  static Future<ApiResponse<dynamic>> getThirdApi(String type) async {
    try {
      var response = await HttpUtils.post(Api.getThirdApi, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type,
        'lang_type': StrUtils.getLang()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 第三方支付，获取订单号和跳转地址
  /// [apiId] 充值接口ID
  /// [money] 充值金额
  ///
  static Future<ApiResponse<dynamic>> getThirdOrder(
      {int? apiId, String? money}) async {
    try {
      var response = await HttpUtils.post(Api.getThirdOrder, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'api_id': apiId,
        'money': money,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取支付订单列表
  /// [page] 当前页
  /// [type] 支付类型,0=全部 1=支付宝 2=微信 3=ios 4=微信小程序 5=paypal 6=braintree_paypal 7=第三方支付 8=区块链
  ///
  static Future<ApiResponse<dynamic>> chargeGetList(int page,
      {String type = '0'}) async {
    try {
      var response = await HttpUtils.post(Api.chargeGetList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type,
        'lang_type': StrUtils.getLang(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取秘宝提现记录列表
  /// [type] 类型，0全部 1表示支付宝，2表示微信，3表示银行卡,4表示币币
  ///
  static Future<ApiResponse<dynamic>> cashGetList(int page,
      {String type = '0'}) async {
    try {
      var response = await HttpUtils.post(Api.cashGetList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type,
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户秘宝提现
  /// [accountid] 账号ID
  /// [money] 用户输入的提现秘宝
  /// [password] 密码
  ///
  static Future<ApiResponse<dynamic>> cashSetCoinCash(
      {int? accountid, String? money, String? password}) async {
    try {
      var response = await HttpUtils.post(Api.cashSetCoinCash, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'accountid': accountid,
        'money': money,
        'password': password
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户秘宝提币
  /// [accountBank] 提现币种
  /// [account] 提现地址
  /// [money] 用户输入的提现秘宝
  /// [password] 密码
  ///
  static Future<ApiResponse<dynamic>> cashSetCoinCash2(
      {String? accountBank,
      String? account,
      String? money,
      String? password}) async {
    try {
      var response = await HttpUtils.post(Api.cashSetCoinCash2, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'account_bank': accountBank,
        'account': account,
        'money': money,
        'password': password
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取提现币种和比例
  ///
  static Future<ApiResponse<dynamic>> cashGetCurrency() async {
    try {
      var response = await HttpUtils.post(Api.cashGetCurrency, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }
}
