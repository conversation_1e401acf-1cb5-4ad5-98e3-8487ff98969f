import 'package:dio/dio.dart';
import 'package:flutter_starry_sky_box/global.dart';

// 错误处理拦截器
class RequestInterceptor extends Interceptor {
  @override
  void onRequest(RequestOptions options, RequestInterceptorHandler handler) {

    String accessToken = Global.accessToken;

    if (accessToken != '') {
      options.copyWith(
        headers: {
          "Content-Type": "application/json",
          'token': accessToken,
        },
      );
    } else {
      options.copyWith(
        headers: {
          "Content-Type": "application/json",
        },
      );
    }

    return super.onRequest(options, handler);
  }

}