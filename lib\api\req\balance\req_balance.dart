import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqBalance {

  ///
  /// 获取秘豆秘宝日志记录（秘豆记录）
  /// [type] 类型,-1:全部,1:收益,2:交易,3:提现
  /// [richType] 钱包类型,coin:秘宝,dou_coin:秘豆
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> getDouLog({
    int? type,
    String? richType,
    int page = 1
  }) async {
    try {
      var response = await HttpUtils.post(Api.getDouLog, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type,
        'rich_type': richType,
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}