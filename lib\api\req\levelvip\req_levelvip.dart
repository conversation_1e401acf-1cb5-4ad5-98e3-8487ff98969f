import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqLevevip {

  ///
  /// 获取用户当前等级信息
  ///
  static Future<ApiResponse<dynamic>> levelVipGetUserInfo() async {
    try {
      var response = await HttpUtils.post(Api.levelVipGetUserInfo, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取等级列表
  ///
  static Future<ApiResponse<dynamic>> levelvipGetList() async {
    try {
      var response = await HttpUtils.post(Api.levelvipGetList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 升级等级
  ///
  static Future<ApiResponse<dynamic>> levelvipBuyLevel(int level) async {
    try {
      var response = await HttpUtils.post(Api.levelvipBuyLevel, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'level': level
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}