plugins {
    id "com.android.application"
    id 'com.google.gms.google-services'
    id "kotlin-android"
    id "dev.flutter.flutter-gradle-plugin"
}

def localProperties = new Properties()
def localPropertiesFile = rootProject.file('local.properties')
if (localPropertiesFile.exists()) {
    localPropertiesFile.withReader('UTF-8') { reader ->
        localProperties.load(reader)
    }
}

def flutterVersionCode = localProperties.getProperty('flutter.versionCode')
if (flutterVersionCode == null) {
    flutterVersionCode = '1'
}

def flutterVersionName = localProperties.getProperty('flutter.versionName')
if (flutterVersionName == null) {
    flutterVersionName = '1.0'
}

def keystorePropertiesFile = rootProject.file("key.properties")
def keystoreProperties = new Properties()
keystoreProperties.load(new FileInputStream(keystorePropertiesFile))

android {
    namespace "com.funshot.video"
    compileSdkVersion 34
    ndkVersion flutter.ndkVersion

    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }

    kotlinOptions {
        jvmTarget = '1.8'
    }

    sourceSets {
        main.java.srcDirs += 'src/main/kotlin'
    }

    signingConfigs {
        release {
            keyAlias keystoreProperties['keyAlias']
            keyPassword keystoreProperties['keyPassword']
            storeFile file(keystoreProperties['storeFile'])
            storePassword keystoreProperties['storePassword']
            v1SigningEnabled true // 启用V1签名
            v2SigningEnabled true // 启用V2签名
        }
    }

    defaultConfig {
        applicationId "com.funshot.video"
        minSdkVersion 24
        targetSdkVersion 34
        versionCode flutterVersionCode.toInteger()
        versionName flutterVersionName

//         ndk {
//             moduleName 'jni' //生成的so名字
// //            abiFilters  /*"armeabi-v7a",*/ 'arm64-v8a',/*'x86_64',*/ 'armeabi'/*,"x86"*/
// //            abiFilters "armeabi-v7a"/*,"x86","armeabi","mips"*/,'arm64-v8a'/*,'x86_64'*/ //主流
//             //输出指定三种abi体系结构下的／so库。目前可有可无。
// //            abiFilters 'armeabi'//车牌识别只支持这个 32位用armeabi  64位用"arm64-v8a", "x86_64"
//             abiFilters "armeabi", "armeabi-v7a", "arm64-v8a", "x86_64", "x86" //https://blog.csdn.net/weixin_39966163/article/details/110361070
//         }

        manifestPlaceholders = [
                OPENINSTALL_APPKEY: "l23zgl",
        ]

        ndk {
            // 设置支持的SO库架构
            // abiFilters 'armeabi-v7a' //, 'arm64-v8a', 'x86', 'x86_64'
            // abiFilters 'armeabi-v7a'
            abiFilters 'arm64-v8a', 'armeabi-v7a', 'x86', 'x86_64'
        }
        multiDexEnabled true
    }

    // buildTypes {
    //     release {
    //         // Signing with the debug keys for now, so `flutter run --release` works.
    //         signingConfig signingConfigs.debug
    //     }
    // }

    buildTypes {
        release {
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            manifestPlaceholders = [applicationName: "android.app.Application"]
        }
        debug {
            // Signing with the debug keys for now, so `flutter run --release` works.
            signingConfig signingConfigs.release
            manifestPlaceholders = [applicationName: "android.app.Application"]
        }
    }
}

flutter {
    source '../..'
}

dependencies {
    // implementation 'com.google.android.gms:play-services-auth:16.0.1'
    testImplementation 'junit:junit:4.12'
    androidTestImplementation 'androidx.test:runner:1.2.0'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.2.0'
    api 'androidx.test:core:1.4.0'
    implementation 'com.google.android.gms:play-services-auth:19.2.0'
    // Import the Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:32.5.0')
    implementation 'com.google.firebase:firebase-analytics'
    implementation("com.google.firebase:firebase-messaging")
    implementation 'androidx.multidex:multidex:2.0.1'
    implementation 'com.android.installreferrer:installreferrer:2.2'
}
