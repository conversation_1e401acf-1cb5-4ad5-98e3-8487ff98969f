import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';

class ReqTrade {

  ///
  /// 获取秘豆交易列表
  /// [page] 当前页
  /// [type] 类型,0=待出售,1=已出售
  ///
  static Future<ApiResponse<dynamic>> tradeGetList(int page, {
    int type = 0
  }) async {
    try {
      var response = await HttpUtils.post(Api.tradeGetList, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 秘豆发布出售
  /// [num] 数量
  /// [amount] 单价
  ///
  static Future<ApiResponse<dynamic>> addOrder({
    int num = 0,
    String amount = '0'
  }) async {
    try {
      var response = await HttpUtils.post(Api.addOrder, data: {
        'num': num,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'amount': amount
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 秘豆市价发布出售
  ///
  static Future<ApiResponse<dynamic>> addTradeOrder({
    int num = 0,
  }) async {
    try {
      var response = await HttpUtils.post(Api.addTradeOrder, data: {
        'num': num,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 秘豆交易购买
  /// [tradeId] 交易id
  /// [num] 数量
  ///
  static Future<ApiResponse<dynamic>> setTrade({
    int num = 0,
    String tradeId = '0'
  }) async {
    try {
      var response = await HttpUtils.post(Api.setTrade, data: {
        'num': num,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'trade_id': tradeId
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 秘豆赠送
  /// [password] 密码
  /// [num] 数量
  /// [touid] touid
  ///
  static Future<ApiResponse<dynamic>> tradeGiveUser({
    String? num,
    String? password,
    String? touid
  }) async {
    try {
      var response = await HttpUtils.post(Api.tradeGiveUser, data: {
        'num': num,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'password': password,
        'touid': touid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取秘豆交易列表
  /// [page] 当前页
  /// [type] 0=支出,1=收入
  ///
  static Future<ApiResponse<dynamic>> tradeGiveLogList(int page, {
    String type = '0'
  }) async {
    try {
      var response = await HttpUtils.post(Api.tradeGiveLogList, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 秘豆交易记录
  /// [type] 类型,-1=全部,0=出售,1=购买
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> tradeGetLogList(int page, {
    String type = '-1'
  }) async {
    try {
      var response = await HttpUtils.post(Api.tradeGetLogList, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'type': type
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取铭文购买列表
  /// [status] 状态,-1=全部,0=进行中,1=已完成
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> goodsGetLogList(int page, {
    String status = '-1'
  }) async {
    try {
      var response = await HttpUtils.post(Api.goodsGetLogList, data: {
        'p': page,
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'status': status
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}