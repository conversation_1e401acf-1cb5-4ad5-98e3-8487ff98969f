import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_starry_sky_box/res/rc.dart';
import 'package:flutter_starry_sky_box/res/styles.dart';
import 'package:flutter_starry_sky_box/utils/crash_logger.dart';
import 'package:flutter_starry_sky_box/widget/screenutil/custom_screenutil.dart';
import 'package:flutter_starry_sky_box/widget/sheet/exit_tip_sheet.dart';
import 'package:flutter_starry_sky_box/widget/toast/custom_toast.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

/// 异常日志查看页面
class CrashLogsPage extends StatefulWidget {
  const CrashLogsPage({super.key});

  @override
  State<CrashLogsPage> createState() => _CrashLogsPageState();
}

class _CrashLogsPageState extends State<CrashLogsPage> {
  List<CrashLogEntry> _logs = [];
  bool _isLoading = true;
  String _fileSize = '';

  @override
  void initState() {
    super.initState();
    _loadLogs();
  }

  /// 加载日志数据
  Future<void> _loadLogs() async {
    setState(() {
      _isLoading = true;
    });

    try {
      // 调试信息
      final filePath = await CrashLogger.getLogFilePath();
      final fileExists = await CrashLogger.logFileExists();
      print('Log file path: $filePath');
      print('Log file exists: $fileExists');

      final logs = await CrashLogger.instance.getAllLogs();
      final fileSize = await CrashLogger.instance.getLogFileSize();

      print('Loaded ${logs.length} logs, file size: $fileSize');

      setState(() {
        _logs = logs;
        _fileSize = fileSize;
        _isLoading = false;
      });
    } catch (e) {
      print('Error loading logs: $e');
      setState(() {
        _isLoading = false;
      });
      CustomToast.showTextToast('加载日志失败: $e');
    }
  }

  /// 清空所有日志
  Future<void> _clearAllLogs() async {
    ExitTipSheet.show(
      title: '确认清空所有异常日志？',
      color: RC.colorEB434B,
      onConfirm: () async {
        try {
          await CrashLogger.instance.clearAllLogs();
          CustomToast.showTextToast('日志已清空');
          Get.back();
          _loadLogs();
        } catch (e) {
          CustomToast.showTextToast('清空日志失败: $e');
        }
      },
    );
  }

  /// 复制日志内容
  void _copyLogContent(CrashLogEntry log) {
    Clipboard.setData(ClipboardData(text: log.fullContent));
    CustomToast.showTextToast('日志内容已复制到剪贴板');
  }

  /// 复制所有日志
  void _copyAllLogs() {
    if (_logs.isEmpty) {
      CustomToast.showTextToast('没有日志可复制');
      return;
    }

    final buffer = StringBuffer();
    buffer.writeln('=== 异常日志报告 ===');
    buffer.writeln('导出时间: ${DateTime.now().toString()}');
    buffer.writeln('日志总数: ${_logs.length}');
    buffer.writeln('文件大小: $_fileSize');
    buffer.writeln('');

    for (int i = 0; i < _logs.length; i++) {
      buffer.writeln('=== 日志 ${i + 1} ===');
      buffer.writeln(_logs[i].fullContent);
      buffer.writeln('');
    }

    Clipboard.setData(ClipboardData(text: buffer.toString()));
    CustomToast.showTextToast('所有日志已复制到剪贴板');
  }

  /// 分享日志文件
  Future<void> _shareLogFile() async {
    if (_logs.isEmpty) {
      CustomToast.showTextToast('没有日志可分享');
      return;
    }

    try {
      CustomToast.showTextToast('正在准备日志文件...');

      if (kIsWeb) {
        // Web平台：复制日志内容到剪贴板
        final logContent = await CrashLogger.createShareableLogContent();
        if (logContent != null) {
          await Clipboard.setData(ClipboardData(text: logContent));
          CustomToast.showTextToast('日志内容已复制到剪贴板，可以粘贴分享');
        } else {
          CustomToast.showTextToast('获取日志内容失败');
        }
      } else {
        // 移动端：创建文件并分享
        final logFile = await CrashLogger.createShareableLogFile();
        if (logFile != null) {
          await Share.shareXFiles(
            [XFile(logFile.path)],
            text: '异常日志文件 - ${DateTime.now().toString().split(' ')[0]}',
            subject: '应用异常日志报告',
          );
        } else {
          CustomToast.showTextToast('创建日志文件失败');
        }
      }
    } catch (e) {
      CustomToast.showTextToast('分享失败: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: RC.white,
      appBar: AppBar(
        backgroundColor: RC.white,
        leading: IconButton(
          onPressed: () => Get.back(),
          icon: SvgPicture.asset('assets/images/svg/message/back.svg'),
        ),
        title: Text(
          '异常日志',
          style: Styles.font_custom(
            fontSize: 17.sp,
            fontWeight: FontWeight.w500,
            color: RC.color1A1A1A,
          ),
        ),
        centerTitle: true,
        actions: [
          if (_logs.isNotEmpty)
            PopupMenuButton<String>(
              onSelected: (value) {
                switch (value) {
                  case 'copy_all':
                    _copyAllLogs();
                    break;
                  case 'share_file':
                    _shareLogFile();
                    break;
                  case 'clear_all':
                    _clearAllLogs();
                    break;
                }
              },
              itemBuilder: (context) => [
                const PopupMenuItem(
                  value: 'copy_all',
                  child: Text('复制所有日志'),
                ),
                const PopupMenuItem(
                  value: 'share_file',
                  child: Text('分享日志文件'),
                ),
                const PopupMenuItem(
                  value: 'clear_all',
                  child: Text('清空所有日志'),
                ),
              ],
              child: Padding(
                padding: EdgeInsets.only(right: 15.w),
                child: Icon(
                  Icons.more_vert,
                  color: RC.color1A1A1A,
                ),
              ),
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_logs.isEmpty) {
      return _buildEmptyState();
    }

    return Column(
      children: [
        _buildHeader(),
        Expanded(
          child: _buildLogsList(),
        ),
      ],
    );
  }

  /// 构建空状态
  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.check_circle_outline,
            size: 64.w,
            color: RC.colorCCCCCC,
          ),
          SizedBox(height: 16.h),
          Text(
            '暂无异常日志',
            style: Styles.font_custom(
              fontSize: 16.sp,
              color: RC.colorCCCCCC,
            ),
          ),
          SizedBox(height: 8.h),
          Text(
            '应用运行正常',
            style: Styles.font_custom(
              fontSize: 14.sp,
              color: RC.colorCCCCCC,
            ),
          ),
        ],
      ),
    );
  }

  /// 构建头部信息
  Widget _buildHeader() {
    return Container(
      margin: EdgeInsets.all(15.w),
      padding: EdgeInsets.all(15.w),
      decoration: BoxDecoration(
        color: RC.colorF5F5F5,
        borderRadius: BorderRadius.circular(8.w),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '日志统计',
                  style: Styles.font_custom(
                    fontSize: 14.sp,
                    fontWeight: FontWeight.w500,
                    color: RC.color1A1A1A,
                  ),
                ),
                SizedBox(height: 8.h),
                Text(
                  '总数: ${_logs.length} 条',
                  style: Styles.font_custom(
                    fontSize: 12.sp,
                    color: RC.color666666,
                  ),
                ),
                Text(
                  '大小: $_fileSize',
                  style: Styles.font_custom(
                    fontSize: 12.sp,
                    color: RC.color666666,
                  ),
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: _loadLogs,
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 12.w, vertical: 6.h),
              decoration: BoxDecoration(
                color: RC.white,
                borderRadius: BorderRadius.circular(4.w),
                border: Border.all(color: RC.colorEFEFEF),
              ),
              child: Text(
                '刷新',
                style: Styles.font_custom(
                  fontSize: 12.sp,
                  color: RC.color1A1A1A,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 构建日志列表
  Widget _buildLogsList() {
    return ListView.builder(
      padding: EdgeInsets.symmetric(horizontal: 15.w),
      itemCount: _logs.length,
      itemBuilder: (context, index) {
        final log = _logs[index];
        return _buildLogItem(log, index);
      },
    );
  }

  /// 构建日志项
  Widget _buildLogItem(CrashLogEntry log, int index) {
    return Container(
      margin: EdgeInsets.only(bottom: 10.h),
      decoration: BoxDecoration(
        color: RC.white,
        borderRadius: BorderRadius.circular(8.w),
        border: Border.all(color: RC.colorEFEFEF),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // 头部信息
          Container(
            padding: EdgeInsets.all(12.w),
            decoration: BoxDecoration(
              color: _getTypeColor(log.type).withOpacity(0.1),
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(8.w),
                topRight: Radius.circular(8.w),
              ),
            ),
            child: Row(
              children: [
                Container(
                  padding: EdgeInsets.symmetric(horizontal: 8.w, vertical: 2.h),
                  decoration: BoxDecoration(
                    color: _getTypeColor(log.type),
                    borderRadius: BorderRadius.circular(4.w),
                  ),
                  child: Text(
                    log.type,
                    style: Styles.font_custom(
                      fontSize: 10.sp,
                      color: RC.white,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(width: 8.w),
                Expanded(
                  child: Text(
                    log.formattedTime,
                    style: Styles.font_custom(
                      fontSize: 12.sp,
                      color: RC.color666666,
                    ),
                  ),
                ),
                GestureDetector(
                  onTap: () => _copyLogContent(log),
                  child: Icon(
                    Icons.copy,
                    size: 16.w,
                    color: RC.color666666,
                  ),
                ),
              ],
            ),
          ),
          // 错误内容
          Padding(
            padding: EdgeInsets.all(12.w),
            child: Text(
              log.shortError,
              style: Styles.font_custom(
                fontSize: 13.sp,
                color: RC.color1A1A1A,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// 根据错误类型获取颜色
  Color _getTypeColor(String type) {
    switch (type.toLowerCase()) {
      case 'flutter error':
        return Colors.red;
      case 'dart error':
        return Colors.orange;
      case 'zone error':
        return Colors.purple;
      default:
        return Colors.grey;
    }
  }
}
