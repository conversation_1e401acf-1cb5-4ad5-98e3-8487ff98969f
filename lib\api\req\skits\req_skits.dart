import 'package:dio/dio.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_starry_sky_box/api/api.dart';
import 'package:flutter_starry_sky_box/api/api_response.dart';
import 'package:flutter_starry_sky_box/api/http_utils.dart';
import 'package:flutter_starry_sky_box/utils/custom_sp_util.dart';
import 'package:flutter_starry_sky_box/utils/str_utils.dart';

class ReqSkits {

  ///
  /// 获取短剧分类列表
  ///
  static Future<ApiResponse<dynamic>> skitsGetClassLists() async {
    try {
      var response = await HttpUtils.post(Api.skitsGetClassLists, data: {
        'lang': StrUtils.getLang()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 根据搜索获取短剧列表
  /// [classid] 短剧分类ID,0为全部
  /// [keyword] 关键字
  /// [orderno] 排序,recommend=推荐榜,new=新剧
  ///
  static Future<ApiResponse<dynamic>> skitsGetSkitsList(int p, {
    int classid = 0,
    String keyword = '',
    String orderno = '' 
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetSkitsList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': p,
        'classid': classid,
        'keyword': keyword,
        'lang': StrUtils.getLang()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }
  ///
  /// 获取短剧详情
  /// [skitsid] 短剧ID
  ///
  static Future<ApiResponse<dynamic>> skitsGetSkits(int skitsid) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetSkits, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 取消\收藏订阅短剧
  ///[skitsid] 短剧ID
  ///
  static Future<ApiResponse<dynamic>> skitsAddCollection(int skitsid) async {
    try {
      var response = await HttpUtils.post(Api.skitsAddCollection, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 取消\点赞短剧
  /// [skitsid] 短剧ID
  ///
  static Future<ApiResponse<dynamic>> skitsAddLike(int skitsid) async {
    try {
      var response = await HttpUtils.post(Api.skitsAddLike, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取短剧评论列表
  /// [skitsid] 短剧ID
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> skitsGetComments({
    int skitsid = 0,
    int page = 1
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetComments, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid,
        'p': page
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 用户评论短剧/回复别人评论
  /// [skitsid] 短剧ID
  /// [content] 内容
  /// [commentid] 回复的评论commentid
  /// [parentid] 回复的评论ID
  /// [touid] 回复的评论UID
  ///
  static Future<ApiResponse<dynamic>> skitsSetComment({
    String? skitsid,
    String? content,

    String? commentid,
    String? parentid,
    String? touid
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsSetComment, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid,
        'content': content,
        'touid': touid,
        'commentid': commentid,
        'parentid': parentid,
        'length': 0
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }


  ///
  /// 删除评论以及子级评论
  /// [skitsid] 短剧ID
  /// [commentid] 评论ID
  /// [commentuid] 评论者用户ID
  ///
  static Future<ApiResponse<dynamic>> skitsDelComments({
    String? skitsid,
    String? commentid,
    String? commentuid
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsDelComments, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid,
        'commentid': commentid,
        'commentuid': commentuid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 评论/回复 点赞数累计
  /// [commentid] 回复的评论commentid
  ///
  static Future<ApiResponse<dynamic>> skitsAddCommentLike({
    int? commentid
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsAddCommentLike, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'commentid': commentid
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 更新短剧当前观看集数
  /// [skitsid] 短剧ID
  /// [num] 集数
  ///
  static Future<ApiResponse<dynamic>> skitsAddView({
    int? skitsid,
    int? num
  }) async {
    try {
      var response = await HttpUtils.post(Api.skitsAddView, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'skitsid': skitsid,
        'num': num
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取猜你喜欢短剧列表
  ///
  static Future<ApiResponse<dynamic>> skitsGetRandList() async {
    try {
      var response = await HttpUtils.post(Api.skitsGetRandList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'lang': StrUtils.getLang()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  /// 
  /// 热门搜索,1查询
  /// 
  static Future<ApiResponse<dynamic>> popularSearches() async {
    try {
      var response = await HttpUtils.post(Api.popularSearches, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': 1,
        'classid': 0,
        'issearch': 1,
        'lang': StrUtils.getLang()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的历史的短剧列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> skitsGetMyView(int page) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetMyView, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的追剧的短剧列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> skitsGetMyCollection(int page) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetMyCollection, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'touid': CustomSpUtil.getUid()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取我的点赞的短剧列表
  /// [page] 页数
  ///
  static Future<ApiResponse<dynamic>> skitsGetMyLike(int page) async {
    try {
      var response = await HttpUtils.post(Api.skitsGetMyLike, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
        'touid': CustomSpUtil.getUid()
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取评论信息列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> skitsCommentLists(int page) async {
    try {
      var response = await HttpUtils.post(Api.skitsCommentLists, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

  ///
  /// 获取短剧评论点赞列表
  /// [page] 当前页
  ///
  static Future<ApiResponse<dynamic>> skitsCommentLikeList(int page) async {
    try {
      var response = await HttpUtils.post(Api.skitsCommentLikeList, data: {
        'uid': CustomSpUtil.getUid(),
        'token': CustomSpUtil.getToken(),
        'p': page,
      });
      return ApiResponse.completed(response);
    } on DioError catch (e) {
      return ApiResponse.error(e.error);
    } finally {
      EasyLoading.dismiss();
    }
  }

}